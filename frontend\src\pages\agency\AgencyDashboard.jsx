import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import {
  HomeIcon,
  UsersIcon,
  EyeIcon,
  HeartIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  PlusIcon,
  PhoneIcon,
  EnvelopeIcon,
  CalendarIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  ClockIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

const AgencyDashboard = () => {
  const { user } = useAuth();
  const [stats] = useState({
    totalProperties: 45,
    activeListings: 38,
    totalViews: 12450,
    totalLeads: 156,
    monthlyRevenue: 45000,
    conversionRate: 12.5,
    avgDaysOnMarket: 28,
    clientSatisfaction: 4.8
  });

  const quickStats = [
    {
      name: 'Active Properties',
      value: stats.activeListings.toString(),
      icon: HomeIcon,
      color: 'bg-blue-500',
      change: '+3',
      changeType: 'positive'
    },
    {
      name: 'Total Views',
      value: stats.totalViews.toLocaleString(),
      icon: EyeIcon,
      color: 'bg-green-500',
      change: '+15%',
      changeType: 'positive'
    },
    {
      name: 'New Leads',
      value: stats.totalLeads.toString(),
      icon: UsersIcon,
      color: 'bg-purple-500',
      change: '+8',
      changeType: 'positive'
    },
    {
      name: 'Monthly Revenue',
      value: `$${stats.monthlyRevenue.toLocaleString()}`,
      icon: CurrencyDollarIcon,
      color: 'bg-yellow-500',
      change: '+12%',
      changeType: 'positive'
    },
    {
      name: 'Conversion Rate',
      value: `${stats.conversionRate}%`,
      icon: TrendingUpIcon,
      color: 'bg-indigo-500',
      change: '+2.1%',
      changeType: 'positive'
    },
    {
      name: 'Avg. Days on Market',
      value: `${stats.avgDaysOnMarket} days`,
      icon: ClockIcon,
      color: 'bg-orange-500',
      change: '-3 days',
      changeType: 'positive'
    }
  ];

  const quickActions = [
    {
      name: 'Add New Property',
      description: 'List a new property',
      href: '/agency/properties/new',
      icon: PlusIcon,
      color: 'bg-blue-500'
    },
    {
      name: 'Manage Properties',
      description: 'View and edit listings',
      href: '/agency/properties',
      icon: HomeIcon,
      color: 'bg-green-500'
    },
    {
      name: 'View Leads',
      description: 'Manage client inquiries',
      href: '/agency/leads',
      icon: UsersIcon,
      color: 'bg-purple-500'
    },
    {
      name: 'Analytics',
      description: 'View detailed reports',
      href: '/agency/analytics',
      icon: ChartBarIcon,
      color: 'bg-indigo-500'
    }
  ];

  const recentLeads = [
    {
      id: 1,
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '+****************',
      property: 'Modern Downtown Condo',
      time: '2 hours ago',
      status: 'new'
    },
    {
      id: 2,
      name: 'Mike Chen',
      email: '<EMAIL>',
      phone: '+****************',
      property: 'Family Home in Suburbs',
      time: '5 hours ago',
      status: 'contacted'
    },
    {
      id: 3,
      name: 'Emily Davis',
      email: '<EMAIL>',
      phone: '+****************',
      property: 'Luxury Penthouse',
      time: '1 day ago',
      status: 'scheduled'
    }
  ];

  const recentProperties = [
    {
      id: 1,
      title: 'Modern Downtown Condo',
      price: '$450,000',
      views: 234,
      favorites: 12,
      status: 'active',
      image: '/api/placeholder/300/200'
    },
    {
      id: 2,
      title: 'Family Home in Suburbs',
      price: '$650,000',
      views: 189,
      favorites: 8,
      status: 'active',
      image: '/api/placeholder/300/200'
    },
    {
      id: 3,
      title: 'Luxury Penthouse',
      price: '$1,200,000',
      views: 456,
      favorites: 23,
      status: 'pending',
      image: '/api/placeholder/300/200'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center justify-between"
          >
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Agency Dashboard
              </h1>
              <p className="mt-2 text-gray-600 dark:text-gray-400">
                Welcome back, {user?.first_name}! Here's your agency overview.
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <PlusIcon className="h-5 w-5 inline mr-2" />
                Add Property
              </motion.button>
            </div>
          </motion.div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8">
          {quickStats.map((stat, index) => (
            <motion.div
              key={stat.name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {stat.name}
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {stat.value}
                  </p>
                  <p className={`text-sm ${stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'}`}>
                    {stat.change} from last month
                  </p>
                </div>
                <div className={`${stat.color} p-3 rounded-lg`}>
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Quick Actions
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {quickActions.map((action, index) => (
              <motion.a
                key={action.name}
                href={action.href}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ scale: 1.02 }}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 hover:shadow-md transition-all duration-200"
              >
                <div className="flex items-center">
                  <div className={`${action.color} p-3 rounded-lg`}>
                    <action.icon className="h-6 w-6 text-white" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                      {action.name}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {action.description}
                    </p>
                  </div>
                </div>
              </motion.a>
            ))}
          </div>
        </div>

        {/* Recent Leads and Properties */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Leads */}
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Recent Leads
            </h2>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
              <div className="p-6">
                <div className="space-y-4">
                  {recentLeads.map((lead, index) => (
                    <motion.div
                      key={lead.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="flex items-center justify-between p-4 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                    >
                      <div className="flex-1">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                          {lead.name}
                        </h4>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {lead.property}
                        </p>
                        <div className="flex items-center space-x-4 mt-2">
                          <span className="flex items-center text-xs text-gray-500">
                            <EnvelopeIcon className="h-3 w-3 mr-1" />
                            {lead.email}
                          </span>
                          <span className="flex items-center text-xs text-gray-500">
                            <PhoneIcon className="h-3 w-3 mr-1" />
                            {lead.phone}
                          </span>
                        </div>
                      </div>
                      <div className="text-right">
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${lead.status === 'new' ? 'bg-blue-100 text-blue-800' :
                            lead.status === 'contacted' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-green-100 text-green-800'
                          }`}>
                          {lead.status}
                        </span>
                        <p className="text-xs text-gray-500 mt-1">{lead.time}</p>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Recent Properties */}
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Recent Properties
            </h2>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
              <div className="p-6">
                <div className="space-y-4">
                  {recentProperties.map((property, index) => (
                    <motion.div
                      key={property.id}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="flex items-center space-x-4 p-4 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                    >
                      <div className="w-16 h-16 bg-gray-200 dark:bg-gray-600 rounded-lg flex items-center justify-center">
                        <HomeIcon className="h-8 w-8 text-gray-400" />
                      </div>
                      <div className="flex-1">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                          {property.title}
                        </h4>
                        <p className="text-lg font-bold text-blue-600 dark:text-blue-400">
                          {property.price}
                        </p>
                        <div className="flex items-center space-x-4 mt-1">
                          <span className="flex items-center text-xs text-gray-500">
                            <EyeIcon className="h-3 w-3 mr-1" />
                            {property.views} views
                          </span>
                          <span className="flex items-center text-xs text-gray-500">
                            <HeartIcon className="h-3 w-3 mr-1" />
                            {property.favorites} favorites
                          </span>
                        </div>
                      </div>
                      <div>
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${property.status === 'active' ? 'bg-green-100 text-green-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                          {property.status}
                        </span>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AgencyDashboard;
