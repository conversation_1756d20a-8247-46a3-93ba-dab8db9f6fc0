<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('property_property_feature', function (Blueprint $table) {
            $table->id();
            $table->foreignId('property_id')->constrained()->onDelete('cascade');
            $table->foreignId('property_feature_id')->constrained()->onDelete('cascade');
            $table->string('value')->nullable(); // For features that have values (e.g., "2 car garage")
            $table->text('notes')->nullable();

            $table->timestamps();

            // Unique constraint to prevent duplicates
            $table->unique(['property_id', 'property_feature_id']);

            // Indexes
            $table->index('property_id');
            $table->index('property_feature_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('property_property_feature');
    }
};
