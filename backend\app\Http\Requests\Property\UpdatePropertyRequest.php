<?php

namespace App\Http\Requests\Property;

use Illuminate\Foundation\Http\FormRequest;

class UpdatePropertyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $property = $this->route('property');

        return auth()->check() &&
               (auth()->user()->agency->id === $property->agency_id ||
                auth()->user()->can('edit properties'));
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $property = $this->route('property');

        return [
            // Basic Information
            'title' => ['sometimes', 'string', 'max:255'],
            'slug' => ['sometimes', 'string', 'max:255', 'unique:properties,slug,' . $property->id],
            'description' => ['sometimes', 'string'],
            'type' => ['sometimes', 'in:house,apartment,condo,townhouse,villa,studio,duplex,penthouse,land,commercial,office,retail,warehouse,industrial'],
            'listing_type' => ['sometimes', 'in:sale,rent,lease'],
            'status' => ['sometimes', 'in:active,inactive,sold,rented,pending,draft'],

            // Pricing
            'price' => ['sometimes', 'numeric', 'min:0'],
            'currency' => ['sometimes', 'string', 'size:3'],
            'price_per_sqft' => ['nullable', 'numeric', 'min:0'],
            'monthly_rent' => ['nullable', 'numeric', 'min:0'],
            'security_deposit' => ['nullable', 'numeric', 'min:0'],
            'maintenance_fee' => ['nullable', 'numeric', 'min:0'],
            'hoa_fee' => ['nullable', 'numeric', 'min:0'],

            // Property Details
            'bedrooms' => ['nullable', 'integer', 'min:0', 'max:50'],
            'bathrooms' => ['nullable', 'integer', 'min:0', 'max:50'],
            'total_area' => ['nullable', 'numeric', 'min:0'],
            'living_area' => ['nullable', 'numeric', 'min:0'],
            'lot_size' => ['nullable', 'numeric', 'min:0'],
            'floors' => ['nullable', 'integer', 'min:1', 'max:200'],
            'parking_spaces' => ['nullable', 'integer', 'min:0', 'max:50'],
            'year_built' => ['nullable', 'integer', 'min:1800', 'max:' . (date('Y') + 5)],
            'year_renovated' => ['nullable', 'integer', 'min:1800', 'max:' . (date('Y') + 5)],

            // Address
            'address_line_1' => ['sometimes', 'string', 'max:255'],
            'address_line_2' => ['nullable', 'string', 'max:255'],
            'city' => ['sometimes', 'string', 'max:255'],
            'state' => ['sometimes', 'string', 'max:255'],
            'postal_code' => ['sometimes', 'string', 'max:20'],
            'country' => ['sometimes', 'string', 'max:255'],
            'neighborhood' => ['nullable', 'string', 'max:255'],
            'latitude' => ['sometimes', 'numeric', 'between:-90,90'],
            'longitude' => ['sometimes', 'numeric', 'between:-180,180'],

            // Features and Amenities
            'features' => ['nullable', 'array'],
            'features.*' => ['string'],
            'amenities' => ['nullable', 'array'],
            'amenities.*' => ['string'],
            'appliances' => ['nullable', 'array'],
            'appliances.*' => ['string'],

            // Property Features (many-to-many)
            'property_features' => ['nullable', 'array'],
            'property_features.*.id' => ['required', 'exists:property_features,id'],
            'property_features.*.value' => ['nullable', 'string'],
            'property_features.*.notes' => ['nullable', 'string'],

            // Media
            'featured_image' => ['nullable', 'string'],
            'image_gallery' => ['nullable', 'array'],
            'image_gallery.*' => ['string'],
            'video_url' => ['nullable', 'url'],
            'virtual_tour_url' => ['nullable', 'url'],
            'floor_plan' => ['nullable', 'string'],

            // SEO
            'meta_title' => ['nullable', 'string', 'max:255'],
            'meta_description' => ['nullable', 'string', 'max:500'],
            'keywords' => ['nullable', 'array'],
            'keywords.*' => ['string'],

            // Marketing
            'is_featured' => ['sometimes', 'boolean'],
            'is_premium' => ['sometimes', 'boolean'],
            'featured_until' => ['nullable', 'date', 'after:today'],

            // Availability
            'available_from' => ['nullable', 'date'],
            'is_negotiable' => ['sometimes', 'boolean'],
            'special_conditions' => ['nullable', 'string'],

            // Energy Efficiency
            'energy_rating' => ['nullable', 'in:A,B,C,D,E,F,G'],
            'energy_consumption' => ['nullable', 'numeric', 'min:0'],
        ];
    }

    /**
     * Get custom error messages for validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'title.string' => 'Property title must be a valid string.',
            'description.string' => 'Property description must be a valid string.',
            'type.in' => 'Invalid property type selected.',
            'listing_type.in' => 'Listing type must be sale, rent, or lease.',
            'price.numeric' => 'Property price must be a valid number.',
            'price.min' => 'Property price cannot be negative.',
            'latitude.between' => 'Latitude must be between -90 and 90.',
            'longitude.between' => 'Longitude must be between -180 and 180.',
            'year_built.min' => 'Year built cannot be before 1800.',
            'year_built.max' => 'Year built cannot be in the future.',
            'bedrooms.min' => 'Number of bedrooms cannot be negative.',
            'bathrooms.min' => 'Number of bathrooms cannot be negative.',
            'slug.unique' => 'A property with this URL already exists.',
        ];
    }

    /**
     * Get custom attribute names for validation errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'title' => 'property title',
            'description' => 'property description',
            'type' => 'property type',
            'listing_type' => 'listing type',
            'price' => 'property price',
            'address_line_1' => 'street address',
            'city' => 'city',
            'state' => 'state/province',
            'postal_code' => 'postal code',
            'country' => 'country',
            'latitude' => 'latitude',
            'longitude' => 'longitude',
            'bedrooms' => 'number of bedrooms',
            'bathrooms' => 'number of bathrooms',
            'total_area' => 'total area',
            'year_built' => 'year built',
            'year_renovated' => 'year renovated',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        if ($this->has('is_featured')) {
            $this->merge(['is_featured' => $this->boolean('is_featured')]);
        }

        if ($this->has('is_premium')) {
            $this->merge(['is_premium' => $this->boolean('is_premium')]);
        }

        if ($this->has('is_negotiable')) {
            $this->merge(['is_negotiable' => $this->boolean('is_negotiable')]);
        }
    }
}
