<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscriptions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('plan_id')->constrained()->onDelete('cascade');
            $table->string('stripe_subscription_id')->nullable();
            $table->string('stripe_customer_id')->nullable();

            // Subscription details
            $table->enum('status', ['active', 'inactive', 'cancelled', 'expired', 'trial', 'past_due'])->default('trial');
            $table->datetime('starts_at');
            $table->datetime('ends_at');
            $table->datetime('trial_ends_at')->nullable();
            $table->datetime('cancelled_at')->nullable();

            // Billing information
            $table->decimal('amount', 10, 2);
            $table->string('currency', 3)->default('USD');
            $table->enum('billing_cycle', ['monthly', 'quarterly', 'yearly']);
            $table->datetime('next_billing_date')->nullable();
            $table->datetime('last_payment_date')->nullable();

            // Usage tracking
            $table->json('usage_limits')->nullable(); // Current usage limits from plan
            $table->json('current_usage')->nullable(); // Current usage statistics

            // Auto-renewal settings
            $table->boolean('auto_renew')->default(true);
            $table->string('cancellation_reason')->nullable();
            $table->text('cancellation_feedback')->nullable();

            // Discount information
            $table->string('coupon_code')->nullable();
            $table->decimal('discount_amount', 10, 2)->nullable();
            $table->decimal('discount_percentage', 5, 2)->nullable();

            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['user_id', 'status']);
            $table->index(['status', 'ends_at']);
            $table->index('next_billing_date');
            $table->index('stripe_subscription_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscriptions');
    }
};
