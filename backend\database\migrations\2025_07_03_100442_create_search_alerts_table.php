<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('search_alerts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('name'); // User-defined name for the alert
            $table->json('search_criteria'); // Stored search parameters
            $table->enum('frequency', ['instant', 'daily', 'weekly', 'monthly'])->default('daily');
            $table->boolean('is_active')->default(true);
            $table->datetime('last_sent_at')->nullable();
            $table->integer('results_count')->default(0); // Number of properties found in last search
            $table->datetime('last_checked_at')->nullable();
            $table->string('email')->nullable(); // Override user's email if needed
            $table->json('notification_preferences')->nullable(); // Email, SMS, push notifications

            $table->timestamps();

            // Indexes
            $table->index(['user_id', 'is_active']);
            $table->index(['is_active', 'frequency']);
            $table->index('last_checked_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('search_alerts');
    }
};
