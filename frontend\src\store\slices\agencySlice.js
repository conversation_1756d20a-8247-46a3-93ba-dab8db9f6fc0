import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

const initialState = {
  profile: null,
  properties: [],
  leads: [],
  analytics: null,
  isLoading: false,
  error: null,
};

const agencySlice = createSlice({
  name: 'agency',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setProfile: (state, action) => {
      state.profile = action.payload;
    },
    updateProfile: (state, action) => {
      state.profile = { ...state.profile, ...action.payload };
    },
  },
});

export const { clearError, setProfile, updateProfile } = agencySlice.actions;
export default agencySlice.reducer;
