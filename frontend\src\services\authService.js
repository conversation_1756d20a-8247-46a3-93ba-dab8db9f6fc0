import api from './api';

const authService = {
  // Login with email and password
  login: async (credentials) => {
    const response = await api.post('/auth/login', credentials);
    return response;
  },

  // Register new user
  register: async (userData) => {
    const response = await api.post('/auth/register', userData);
    return response;
  },

  // Logout user
  logout: async () => {
    const response = await api.post('/auth/logout');
    return response;
  },

  // Get current authenticated user
  getCurrentUser: async () => {
    const response = await api.get('/auth/user');
    return response;
  },

  // Social login (Google, Facebook, Apple)
  socialLogin: async (provider, token) => {
    const response = await api.post(`/auth/social/${provider}`, {
      token,
    });
    return response;
  },

  // Forgot password
  forgotPassword: async (email) => {
    const response = await api.post('/auth/forgot-password', {
      email,
    });
    return response;
  },

  // Reset password
  resetPassword: async (token, email, password, passwordConfirmation) => {
    const response = await api.post('/auth/reset-password', {
      token,
      email,
      password,
      password_confirmation: passwordConfirmation,
    });
    return response;
  },

  // Change password
  changePassword: async (currentPassword, newPassword, passwordConfirmation) => {
    const response = await api.post('/auth/change-password', {
      current_password: currentPassword,
      password: newPassword,
      password_confirmation: passwordConfirmation,
    });
    return response;
  },

  // Update profile
  updateProfile: async (profileData) => {
    const response = await api.put('/auth/profile', profileData);
    return response;
  },

  // Upload profile picture
  uploadProfilePicture: async (file) => {
    const formData = new FormData();
    formData.append('profile_picture', file);
    
    const response = await api.post('/auth/profile/picture', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response;
  },

  // Enable 2FA
  enable2FA: async () => {
    const response = await api.post('/auth/2fa/enable');
    return response;
  },

  // Disable 2FA
  disable2FA: async (code) => {
    const response = await api.post('/auth/2fa/disable', {
      code,
    });
    return response;
  },

  // Verify 2FA code
  verify2FA: async (code) => {
    const response = await api.post('/auth/2fa/verify', {
      code,
    });
    return response;
  },

  // Get 2FA QR code
  get2FAQRCode: async () => {
    const response = await api.get('/auth/2fa/qr-code');
    return response;
  },

  // Verify email
  verifyEmail: async (token) => {
    const response = await api.post('/auth/verify-email', {
      token,
    });
    return response;
  },

  // Resend email verification
  resendEmailVerification: async () => {
    const response = await api.post('/auth/resend-verification');
    return response;
  },

  // Get user permissions
  getUserPermissions: async () => {
    const response = await api.get('/auth/permissions');
    return response;
  },

  // Check if user has permission
  hasPermission: (userPermissions, permission) => {
    return userPermissions.includes(permission);
  },

  // Check if user has role
  hasRole: (userRole, role) => {
    return userRole === role;
  },

  // Check if user has any of the roles
  hasAnyRole: (userRole, roles) => {
    return roles.includes(userRole);
  },
};

export default authService;
