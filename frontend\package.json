{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@reduxjs/toolkit": "^2.8.2", "axios": "^1.10.0", "framer-motion": "^12.23.0", "leaflet": "^1.9.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-grid-layout": "^1.5.2", "react-hook-form": "^7.59.0", "react-hot-toast": "^2.5.2", "react-intersection-observer": "^9.16.0", "react-leaflet": "^5.0.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.3", "recharts": "^3.0.2", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.29.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "vite": "^7.0.0"}}