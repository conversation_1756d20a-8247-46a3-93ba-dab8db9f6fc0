<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('favorites', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('property_id')->constrained()->onDelete('cascade');
            $table->text('notes')->nullable(); // User's personal notes about the property
            $table->json('tags')->nullable(); // User-defined tags for organization
            $table->boolean('is_notification_enabled')->default(true); // Notify about price changes, etc.

            $table->timestamps();

            // Unique constraint to prevent duplicate favorites
            $table->unique(['user_id', 'property_id']);

            // Indexes
            $table->index('user_id');
            $table->index('property_id');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('favorites');
    }
};
