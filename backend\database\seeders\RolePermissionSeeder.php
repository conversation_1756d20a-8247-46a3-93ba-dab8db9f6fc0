<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // User Management
            'view users',
            'create users',
            'edit users',
            'delete users',
            'manage user roles',

            // Agency Management
            'view agencies',
            'create agencies',
            'edit agencies',
            'delete agencies',
            'verify agencies',

            // Property Management
            'view properties',
            'create properties',
            'edit properties',
            'delete properties',
            'manage property features',
            'approve properties',

            // Plan Management
            'view plans',
            'create plans',
            'edit plans',
            'delete plans',
            'manage subscriptions',

            // Content Management
            'manage static pages',
            'manage site settings',
            'view analytics',
            'manage notifications',

            // Communication
            'send messages',
            'view all messages',
            'moderate messages',

            // Reviews and Ratings
            'view reviews',
            'moderate reviews',
            'delete reviews',

            // Reports and Analytics
            'view reports',
            'export data',
            'view system logs',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles and assign permissions

        // Admin Role - Full access
        $adminRole = Role::create(['name' => 'admin']);
        $adminRole->givePermissionTo(Permission::all());

        // Agency Role - Property and business management
        $agencyRole = Role::create(['name' => 'agency']);
        $agencyRole->givePermissionTo([
            'view properties',
            'create properties',
            'edit properties',
            'delete properties',
            'view plans',
            'manage subscriptions',
            'send messages',
            'view reviews',
            'view reports',
        ]);

        // User Role - Basic user permissions
        $userRole = Role::create(['name' => 'user']);
        $userRole->givePermissionTo([
            'view properties',
            'view plans',
            'manage subscriptions',
            'send messages',
            'view reviews',
        ]);

        $this->command->info('Roles and permissions created successfully!');
    }
}
