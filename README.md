# Real Estate Platform

A modern real estate platform built with <PERSON><PERSON> (backend) and <PERSON><PERSON> (frontend), featuring glassmorphism design, neumorphism dashboards, and comprehensive property management.

## 🏗️ Project Structure

```
RealEstate/
├── backend/                 # Laravel API Backend
│   ├── app/
│   │   ├── Http/Controllers/
│   │   ├── Models/
│   │   ├── Services/
│   │   └── Traits/
│   ├── database/
│   │   ├── migrations/
│   │   └── seeders/
│   └── routes/
├── frontend/                # React Frontend
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── hooks/
│   │   ├── services/
│   │   ├── store/
│   │   └── utils/
│   └── public/
└── docs/                   # Documentation
```

## 🚀 Features

### Core Features
- **Multi-role Authentication**: Admin, Agency, User roles with unified login
- **Property Management**: CRUD operations with image/media handling
- **Plan Management**: Subscription plans for users and agencies
- **Real-time Communication**: Messaging between agencies and users
- **Advanced Search**: Filters, map integration, location-based search
- **Gamification**: Badges, points, achievements system

### Design Features
- **Glassmorphism UI**: Modern glass-like interface elements
- **Neumorphism Dashboards**: Soft, extruded design for admin panels
- **Dark/Light Mode**: Theme switching capability
- **Responsive Design**: Mobile-first approach
- **Accessibility**: WCAG-compliant features

### Technical Features
- **Laravel Sanctum**: API authentication
- **Social Authentication**: Google, Facebook, Apple login
- **Real-time Updates**: WebSocket integration
- **Image Processing**: Intervention Image for media handling
- **Search Engine**: Laravel Scout integration
- **Permission System**: Spatie Laravel Permission

## 🛠️ Tech Stack

### Backend
- **Laravel 12**: PHP framework
- **MySQL**: Database
- **Laravel Sanctum**: Authentication
- **Laravel Socialite**: Social login
- **Spatie Permission**: Role & permission management
- **Intervention Image**: Image processing
- **Pusher**: Real-time communication
- **Laravel Scout**: Search functionality

### Frontend
- **React 18**: UI library
- **Vite**: Build tool
- **Tailwind CSS**: Styling framework
- **Framer Motion**: Animations
- **React Router**: Navigation
- **Redux Toolkit**: State management
- **React Leaflet**: Map integration
- **React Hook Form**: Form handling
- **Recharts**: Data visualization

## 📋 Installation

### Prerequisites
- PHP 8.2+
- Node.js 18+
- Composer
- MySQL

### Backend Setup
```bash
cd backend
composer install
cp .env.example .env
php artisan key:generate
php artisan migrate
php artisan db:seed
```

### Frontend Setup
```bash
cd frontend
npm install
npm run dev
```

## 🎨 Design System

### Color Palette
- **Primary**: Blue gradient (#3b82f6 to #1d4ed8)
- **Glass Effects**: Semi-transparent overlays
- **Neumorphism**: Soft shadows and highlights

### Components
- **Glass Cards**: Backdrop blur with transparency
- **Neomorphic Buttons**: Soft, extruded appearance
- **Animated Transitions**: Smooth page transitions
- **Interactive Maps**: Property location visualization

## 📱 User Roles & Permissions

### Admin
- Manage all users and agencies
- Configure platform settings
- Manage subscription plans
- View analytics and reports
- Edit static pages content

### Agency
- Manage property listings
- Track leads and inquiries
- View performance analytics
- Manage subscription plans
- Communicate with users

### User
- Browse and search properties
- Save favorite properties
- Subscribe to plans
- Communicate with agencies
- Receive personalized recommendations

## 🔧 Development

### Running the Application
```bash
# Backend (Laravel)
cd backend
php artisan serve

# Frontend (React)
cd frontend
npm run dev
```

### Testing
```bash
# Backend tests
cd backend
php artisan test

# Frontend tests
cd frontend
npm run test
```

## 📚 API Documentation

API documentation will be available at `/api/documentation` when the backend is running.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📄 License

This project is licensed under the MIT License.
