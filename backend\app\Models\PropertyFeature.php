<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PropertyFeature extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'slug',
        'description',
        'icon',
        'category',
        'is_active',
        'sort_order',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Get the properties that have this feature.
     */
    public function properties()
    {
        return $this->belongsToMany(Property::class, 'property_property_feature')
                    ->withPivot('value', 'notes')
                    ->withTimestamps();
    }

    /**
     * Scope to get active features.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get features by category.
     */
    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Get the feature icon with fallback.
     */
    public function getIconWithFallbackAttribute(): string
    {
        return $this->icon ?: 'check-circle';
    }

    /**
     * Get the category badge.
     */
    public function getCategoryBadgeAttribute(): array
    {
        $badges = [
            'interior' => ['text' => 'Interior', 'color' => 'blue'],
            'exterior' => ['text' => 'Exterior', 'color' => 'green'],
            'amenity' => ['text' => 'Amenity', 'color' => 'purple'],
            'appliance' => ['text' => 'Appliance', 'color' => 'orange'],
            'security' => ['text' => 'Security', 'color' => 'red'],
            'energy' => ['text' => 'Energy', 'color' => 'yellow'],
            'accessibility' => ['text' => 'Accessibility', 'color' => 'indigo'],
            'other' => ['text' => 'Other', 'color' => 'gray'],
        ];

        return $badges[$this->category] ?? ['text' => 'Unknown', 'color' => 'gray'];
    }
}
