<?php

namespace App\Http\Requests\Plan;

use Illuminate\Foundation\Http\FormRequest;

class CreatePlanRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && auth()->user()->can('create plans');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'slug' => ['nullable', 'string', 'max:255', 'unique:plans,slug'],
            'description' => ['nullable', 'string'],
            'type' => ['required', 'in:user,agency'],
            'price' => ['required', 'numeric', 'min:0'],
            'billing_cycle' => ['required', 'in:monthly,quarterly,yearly'],
            'billing_cycle_months' => ['required', 'integer', 'min:1'],
            'features' => ['nullable', 'array'],
            'features.*' => ['string'],

            // Agency-specific features
            'property_listing_limit' => ['nullable', 'integer', 'min:0'],
            'featured_listing_limit' => ['nullable', 'integer', 'min:0'],
            'photo_limit_per_property' => ['nullable', 'integer', 'min:0'],
            'video_limit_per_property' => ['nullable', 'integer', 'min:0'],
            'virtual_tour_enabled' => ['boolean'],
            'analytics_enabled' => ['boolean'],
            'lead_management_enabled' => ['boolean'],
            'priority_support_enabled' => ['boolean'],
            'api_access_enabled' => ['boolean'],

            // User-specific features
            'saved_properties_limit' => ['nullable', 'integer', 'min:0'],
            'search_alerts_limit' => ['nullable', 'integer', 'min:0'],
            'advanced_search_enabled' => ['boolean'],
            'property_recommendations_enabled' => ['boolean'],
            'market_insights_enabled' => ['boolean'],

            // Plan settings
            'is_popular' => ['boolean'],
            'is_active' => ['boolean'],
            'sort_order' => ['integer', 'min:0'],
            'stripe_price_id' => ['nullable', 'string'],
            'color_scheme' => ['nullable', 'string'],
            'icon' => ['nullable', 'string'],

            // Trial settings
            'has_trial' => ['boolean'],
            'trial_days' => ['nullable', 'integer', 'min:0'],

            // Pricing
            'setup_fee' => ['numeric', 'min:0'],
            'discount_percentage' => ['nullable', 'numeric', 'min:0', 'max:100'],
            'discount_valid_until' => ['nullable', 'date', 'after:today'],
        ];
    }

    /**
     * Get custom error messages for validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Plan name is required.',
            'type.required' => 'Plan type is required.',
            'type.in' => 'Plan type must be either user or agency.',
            'price.required' => 'Plan price is required.',
            'price.numeric' => 'Plan price must be a valid number.',
            'price.min' => 'Plan price cannot be negative.',
            'billing_cycle.required' => 'Billing cycle is required.',
            'billing_cycle.in' => 'Billing cycle must be monthly, quarterly, or yearly.',
            'billing_cycle_months.required' => 'Billing cycle months is required.',
            'billing_cycle_months.integer' => 'Billing cycle months must be an integer.',
            'billing_cycle_months.min' => 'Billing cycle months must be at least 1.',
            'slug.unique' => 'A plan with this slug already exists.',
            'discount_percentage.max' => 'Discount percentage cannot exceed 100%.',
            'discount_valid_until.after' => 'Discount valid until date must be in the future.',
        ];
    }

    /**
     * Get custom attribute names for validation errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'name' => 'plan name',
            'type' => 'plan type',
            'price' => 'plan price',
            'billing_cycle' => 'billing cycle',
            'billing_cycle_months' => 'billing cycle months',
            'property_listing_limit' => 'property listing limit',
            'featured_listing_limit' => 'featured listing limit',
            'photo_limit_per_property' => 'photo limit per property',
            'video_limit_per_property' => 'video limit per property',
            'saved_properties_limit' => 'saved properties limit',
            'search_alerts_limit' => 'search alerts limit',
            'setup_fee' => 'setup fee',
            'discount_percentage' => 'discount percentage',
            'discount_valid_until' => 'discount valid until',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'virtual_tour_enabled' => $this->boolean('virtual_tour_enabled'),
            'analytics_enabled' => $this->boolean('analytics_enabled'),
            'lead_management_enabled' => $this->boolean('lead_management_enabled'),
            'priority_support_enabled' => $this->boolean('priority_support_enabled'),
            'api_access_enabled' => $this->boolean('api_access_enabled'),
            'advanced_search_enabled' => $this->boolean('advanced_search_enabled'),
            'property_recommendations_enabled' => $this->boolean('property_recommendations_enabled'),
            'market_insights_enabled' => $this->boolean('market_insights_enabled'),
            'is_popular' => $this->boolean('is_popular'),
            'is_active' => $this->boolean('is_active', true),
            'has_trial' => $this->boolean('has_trial'),
            'setup_fee' => $this->input('setup_fee', 0),
            'sort_order' => $this->input('sort_order', 0),
        ]);

        // Set billing cycle months based on billing cycle if not provided
        if (!$this->has('billing_cycle_months') && $this->has('billing_cycle')) {
            $months = [
                'monthly' => 1,
                'quarterly' => 3,
                'yearly' => 12,
            ];

            $this->merge([
                'billing_cycle_months' => $months[$this->billing_cycle] ?? 1
            ]);
        }
    }
}
