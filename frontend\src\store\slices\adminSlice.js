import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

const initialState = {
  users: [],
  agencies: [],
  properties: [],
  plans: [],
  analytics: null,
  isLoading: false,
  error: null,
};

const adminSlice = createSlice({
  name: 'admin',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setUsers: (state, action) => {
      state.users = action.payload;
    },
    setAgencies: (state, action) => {
      state.agencies = action.payload;
    },
  },
});

export const { clearError, setUsers, setAgencies } = adminSlice.actions;
export default adminSlice.reducer;
