<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\EmailVerificationController;
use App\Http\Controllers\Api\PasswordResetController;
use App\Http\Controllers\Api\PlanController;
use App\Http\Controllers\Api\SocialAuthController;
use App\Http\Controllers\Api\SubscriptionController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public Authentication Routes
Route::prefix('auth')->group(function () {
    // Basic Authentication
    Route::post('/register', [AuthController::class, 'register']);
    Route::post('/login', [AuthController::class, 'login']);
    
    // Password Reset
    Route::post('/forgot-password', [PasswordResetController::class, 'sendResetLink']);
    Route::post('/reset-password', [PasswordResetController::class, 'resetPassword']);
    
    // Social Authentication
    Route::prefix('social')->group(function () {
        Route::get('/{provider}/redirect', [SocialAuthController::class, 'redirectToProvider']);
        Route::get('/{provider}/callback', [SocialAuthController::class, 'handleProviderCallback']);
        Route::post('/{provider}/token', [SocialAuthController::class, 'loginWithToken']);
    });
    
    // Email Verification (public)
    Route::post('/verify-email-token', [EmailVerificationController::class, 'verifyEmailWithToken']);
});

// Protected Authentication Routes
Route::middleware('auth:sanctum')->prefix('auth')->group(function () {
    // User Management
    Route::get('/user', [AuthController::class, 'user']);
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::post('/logout-all', [AuthController::class, 'logoutAll']);
    Route::post('/refresh', [AuthController::class, 'refresh']);
    
    // Password Management
    Route::post('/change-password', [PasswordResetController::class, 'changePassword']);
    
    // Email Verification
    Route::post('/email/verification-notification', [EmailVerificationController::class, 'sendVerificationEmail']);
    Route::get('/email/verify/{id}/{hash}', [EmailVerificationController::class, 'verifyEmail'])
        ->middleware(['signed', 'throttle:6,1'])
        ->name('verification.verify');
    Route::get('/email/verification-status', [EmailVerificationController::class, 'checkVerificationStatus']);
});

// Public Plan Routes
Route::prefix('plans')->group(function () {
    Route::get('/', [PlanController::class, 'index']);
    Route::get('/popular', [PlanController::class, 'popular']);
    Route::get('/user-type/{userType}', [PlanController::class, 'getByUserType']);
    Route::get('/{plan}', [PlanController::class, 'show']);
    Route::post('/compare', [PlanController::class, 'compare']);
});

// Protected Plan Management Routes (Admin only)
Route::middleware(['auth:sanctum', 'role:admin'])->prefix('admin/plans')->group(function () {
    Route::post('/', [PlanController::class, 'store']);
    Route::put('/{plan}', [PlanController::class, 'update']);
    Route::delete('/{plan}', [PlanController::class, 'destroy']);
});

// Protected Subscription Routes
Route::middleware('auth:sanctum')->prefix('subscriptions')->group(function () {
    Route::get('/', [SubscriptionController::class, 'index']);
    Route::get('/current', [SubscriptionController::class, 'current']);
    Route::post('/', [SubscriptionController::class, 'store']);
    Route::get('/{subscription}', [SubscriptionController::class, 'show']);
    Route::put('/{subscription}', [SubscriptionController::class, 'update']);
    Route::post('/{subscription}/cancel', [SubscriptionController::class, 'cancel']);
    Route::post('/{subscription}/renew', [SubscriptionController::class, 'renew']);
    Route::get('/{subscription}/usage', [SubscriptionController::class, 'usage']);
});

// Health Check
Route::get('/health', function () {
    return response()->json([
        'status' => 'ok',
        'timestamp' => now(),
        'version' => '1.0.0'
    ]);
});

// Fallback route for API
Route::fallback(function () {
    return response()->json([
        'message' => 'API endpoint not found'
    ], 404);
});
