import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import {
  HeartIcon,
  MapPinIcon,
  HomeIcon,
  CameraIcon,
  EyeIcon,
  StarIcon,
  ShareIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import propertyService from '../../services/propertyService';

const PropertyListItem = ({ 
  property, 
  onFavoriteToggle, 
  onShare,
  className = '',
  showAgency = true,
  showStats = true 
}) => {
  const [isImageLoading, setIsImageLoading] = useState(true);
  const [isFavorited, setIsFavorited] = useState(property.is_favorited || false);

  const handleFavoriteClick = async (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    try {
      setIsFavorited(!isFavorited);
      if (onFavoriteToggle) {
        await onFavoriteToggle(property.id, !isFavorited);
      }
    } catch (error) {
      // Revert on error
      setIsFavorited(isFavorited);
      console.error('Failed to toggle favorite:', error);
    }
  };

  const handleShareClick = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (onShare) {
      onShare(property);
    }
  };

  const getImageUrl = () => {
    if (property.featured_image) {
      return property.featured_image;
    }
    if (property.images && property.images.length > 0) {
      return property.images[0].url || property.images[0].medium_url;
    }
    return '/images/property-placeholder.jpg';
  };

  const getBadgeColor = (status) => {
    const colors = {
      'active': 'bg-green-500',
      'sold': 'bg-blue-500',
      'rented': 'bg-purple-500',
      'pending': 'bg-yellow-500',
      'inactive': 'bg-gray-500'
    };
    return colors[status] || 'bg-gray-500';
  };

  return (
    <motion.div
      className={`property-list-item ${className}`}
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Link to={`/properties/${property.slug}`}>
        <div className="glass-card p-4 hover:shadow-xl transition-all duration-300 
                        transform hover:-translate-y-1">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Image */}
            <div className="relative w-full lg:w-80 h-48 lg:h-40 flex-shrink-0 overflow-hidden rounded-xl">
              <motion.img
                src={getImageUrl()}
                alt={property.title}
                className="w-full h-full object-cover transition-transform duration-500 hover:scale-110"
                onLoad={() => setIsImageLoading(false)}
                initial={{ scale: 1.1 }}
                animate={{ scale: isImageLoading ? 1.1 : 1 }}
                transition={{ duration: 0.5 }}
              />

              {/* Loading Skeleton */}
              {isImageLoading && (
                <div className="absolute inset-0 bg-gray-200 dark:bg-gray-700 animate-pulse" />
              )}

              {/* Image Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent" />

              {/* Status Badge */}
              {property.status && property.status !== 'active' && (
                <div className={`absolute top-2 left-2 px-2 py-1 rounded-full text-xs font-medium text-white
                                ${getBadgeColor(property.status)}`}>
                  {property.status_badge?.text || property.status}
                </div>
              )}

              {/* Featured Badge */}
              {property.is_featured && (
                <div className="absolute top-2 right-2 px-2 py-1 bg-gradient-to-r from-yellow-400 to-orange-500 
                                text-white text-xs font-medium rounded-full flex items-center gap-1">
                  <StarIcon className="h-3 w-3" />
                  Featured
                </div>
              )}

              {/* Image Count */}
              {property.images && property.images.length > 1 && (
                <div className="absolute bottom-2 right-2 px-2 py-1 bg-black/50 backdrop-blur-sm 
                                text-white text-xs rounded-full flex items-center gap-1">
                  <CameraIcon className="h-3 w-3" />
                  {property.images.length}
                </div>
              )}

              {/* Price Overlay */}
              <div className="absolute bottom-2 left-2">
                <div className="text-white">
                  <div className="text-xl font-bold">
                    {propertyService.formatPrice(property.price, property.currency)}
                  </div>
                  {property.listing_type === 'rent' && (
                    <div className="text-xs opacity-90">/month</div>
                  )}
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between mb-2">
                {/* Title */}
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white line-clamp-2 
                               hover:text-blue-600 dark:hover:text-blue-400 transition-colors flex-1 mr-4">
                  {property.title}
                </h3>

                {/* Action Buttons */}
                <div className="flex gap-2 flex-shrink-0">
                  <motion.button
                    onClick={handleFavoriteClick}
                    className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    {isFavorited ? (
                      <HeartSolidIcon className="h-5 w-5 text-red-500" />
                    ) : (
                      <HeartIcon className="h-5 w-5 text-gray-400" />
                    )}
                  </motion.button>

                  <motion.button
                    onClick={handleShareClick}
                    className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <ShareIcon className="h-5 w-5 text-gray-400" />
                  </motion.button>
                </div>
              </div>

              {/* Location */}
              <div className="flex items-center text-gray-600 dark:text-gray-400 mb-3">
                <MapPinIcon className="h-4 w-4 mr-1 flex-shrink-0" />
                <span className="text-sm truncate">
                  {property.full_address || `${property.city}, ${property.state}`}
                </span>
              </div>

              {/* Property Details */}
              <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mb-3">
                {property.bedrooms && (
                  <div className="flex items-center gap-1">
                    <HomeIcon className="h-4 w-4" />
                    <span>{property.bedrooms} bed</span>
                  </div>
                )}
                {property.bathrooms && (
                  <div className="flex items-center gap-1">
                    <span>{property.bathrooms} bath</span>
                  </div>
                )}
                {property.total_area && (
                  <div className="flex items-center gap-1">
                    <span>{propertyService.formatArea(property.total_area)}</span>
                  </div>
                )}
                {property.year_built && (
                  <div className="flex items-center gap-1">
                    <CalendarIcon className="h-4 w-4" />
                    <span>Built {property.year_built}</span>
                  </div>
                )}
              </div>

              {/* Description */}
              {property.description && (
                <p className="text-gray-600 dark:text-gray-400 text-sm line-clamp-2 mb-3">
                  {property.description}
                </p>
              )}

              {/* Bottom Row */}
              <div className="flex items-center justify-between">
                {/* Agency Info */}
                {showAgency && property.agency && (
                  <div className="flex items-center gap-2">
                    {property.agency.logo && (
                      <img
                        src={property.agency.logo}
                        alt={property.agency.name}
                        className="w-6 h-6 rounded-full object-cover"
                      />
                    )}
                    <span className="text-sm text-gray-600 dark:text-gray-400 truncate">
                      {property.agency.name}
                    </span>
                    {property.agency.is_verified && (
                      <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                        <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    )}
                  </div>
                )}

                {/* Stats */}
                {showStats && (
                  <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                    {property.views_count > 0 && (
                      <div className="flex items-center gap-1">
                        <EyeIcon className="h-3 w-3" />
                        <span>{property.views_count}</span>
                      </div>
                    )}
                    {property.favorites_count > 0 && (
                      <div className="flex items-center gap-1">
                        <HeartIcon className="h-3 w-3" />
                        <span>{property.favorites_count}</span>
                      </div>
                    )}
                    {property.average_rating > 0 && (
                      <div className="flex items-center gap-1">
                        <StarIcon className="h-3 w-3" />
                        <span>{property.average_rating.toFixed(1)}</span>
                      </div>
                    )}
                    <div className="text-xs">
                      {new Date(property.created_at).toLocaleDateString()}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </Link>
    </motion.div>
  );
};

export default PropertyListItem;
