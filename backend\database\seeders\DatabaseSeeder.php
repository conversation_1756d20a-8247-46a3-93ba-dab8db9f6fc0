<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Seed roles and permissions first
        $this->call([
            RolePermissionSeeder::class,
        ]);

        // Create test users
        $this->createTestUsers();
    }

    /**
     * Create test users for development
     */
    private function createTestUsers(): void
    {
        // Create admin user
        $admin = User::create([
            'first_name' => 'Admin',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin',
            'status' => 'active',
            'email_verified_at' => now(),
        ]);
        $admin->assignRole('admin');

        // Create agency user
        $agency = User::create([
            'first_name' => 'Agency',
            'last_name' => 'Owner',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'agency',
            'status' => 'active',
            'email_verified_at' => now(),
        ]);
        $agency->assignRole('agency');

        // Create regular user
        $user = User::create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'user',
            'status' => 'active',
            'email_verified_at' => now(),
        ]);
        $user->assignRole('user');

        $this->command->info('Test users created successfully!');
        $this->command->info('Admin: <EMAIL> / password');
        $this->command->info('Agency: <EMAIL> / password');
        $this->command->info('User: <EMAIL> / password');
    }
}
