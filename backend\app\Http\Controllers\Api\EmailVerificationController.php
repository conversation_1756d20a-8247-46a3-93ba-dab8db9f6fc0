<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Auth\Events\Verified;
use Illuminate\Foundation\Auth\EmailVerificationRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class EmailVerificationController extends Controller
{
    /**
     * Send email verification notification
     */
    public function sendVerificationEmail(Request $request): JsonResponse
    {
        try {
            $user = $request->user();

            if ($user->hasVerifiedEmail()) {
                return response()->json([
                    'message' => 'Email is already verified.'
                ]);
            }

            $user->sendEmailVerificationNotification();

            return response()->json([
                'message' => 'Verification email sent successfully.'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to send verification email',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Verify email address
     */
    public function verifyEmail(EmailVerificationRequest $request): JsonResponse
    {
        try {
            $user = $request->user();

            if ($user->hasVerifiedEmail()) {
                return response()->json([
                    'message' => 'Email is already verified.'
                ]);
            }

            if ($user->markEmailAsVerified()) {
                event(new Verified($user));
            }

            return response()->json([
                'message' => 'Email verified successfully.'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to verify email',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Verify email with token (for API clients)
     */
    public function verifyEmailWithToken(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'id' => ['required', 'integer'],
                'hash' => ['required', 'string'],
                'expires' => ['required', 'integer'],
                'signature' => ['required', 'string'],
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'message' => 'Invalid verification link',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = User::findOrFail($request->id);

            // Check if the hash matches
            if (!hash_equals((string) $request->hash, sha1($user->getEmailForVerification()))) {
                return response()->json([
                    'message' => 'Invalid verification link'
                ], 400);
            }

            // Check if link has expired
            if ($request->expires < time()) {
                return response()->json([
                    'message' => 'Verification link has expired'
                ], 400);
            }

            if ($user->hasVerifiedEmail()) {
                return response()->json([
                    'message' => 'Email is already verified.'
                ]);
            }

            if ($user->markEmailAsVerified()) {
                event(new Verified($user));
            }

            return response()->json([
                'message' => 'Email verified successfully.'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to verify email',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check email verification status
     */
    public function checkVerificationStatus(Request $request): JsonResponse
    {
        try {
            $user = $request->user();

            return response()->json([
                'is_verified' => $user->hasVerifiedEmail(),
                'email' => $user->email,
                'verified_at' => $user->email_verified_at,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to check verification status',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
