import axios from 'axios';
import { store } from '../store';
import { showToast } from '../store/slices/uiSlice';
import { clearCredentials } from '../store/slices/authSlice';

// Create axios instance
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:8000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    const { response } = error;

    if (response) {
      switch (response.status) {
        case 401:
          // Unauthorized - clear credentials and redirect to login
          store.dispatch(clearCredentials());
          store.dispatch(showToast({
            message: 'Session expired. Please login again.',
            type: 'error'
          }));
          window.location.href = '/login';
          break;

        case 403:
          // Forbidden
          store.dispatch(showToast({
            message: 'You do not have permission to perform this action.',
            type: 'error'
          }));
          break;

        case 404:
          // Not found
          store.dispatch(showToast({
            message: 'The requested resource was not found.',
            type: 'error'
          }));
          break;

        case 422:
          // Validation error
          const validationErrors = response.data.errors;
          if (validationErrors) {
            const firstError = Object.values(validationErrors)[0][0];
            store.dispatch(showToast({
              message: firstError,
              type: 'error'
            }));
          }
          break;

        case 429:
          // Too many requests
          store.dispatch(showToast({
            message: 'Too many requests. Please try again later.',
            type: 'error'
          }));
          break;

        case 500:
          // Server error
          store.dispatch(showToast({
            message: 'Internal server error. Please try again later.',
            type: 'error'
          }));
          break;

        default:
          // Generic error
          const message = response.data?.message || 'An error occurred. Please try again.';
          store.dispatch(showToast({
            message,
            type: 'error'
          }));
      }
    } else if (error.request) {
      // Network error
      store.dispatch(showToast({
        message: 'Network error. Please check your connection.',
        type: 'error'
      }));
    } else {
      // Other error
      store.dispatch(showToast({
        message: 'An unexpected error occurred.',
        type: 'error'
      }));
    }

    return Promise.reject(error);
  }
);

// Helper functions for different HTTP methods
export const apiHelpers = {
  get: (url, config = {}) => api.get(url, config),
  post: (url, data = {}, config = {}) => api.post(url, data, config),
  put: (url, data = {}, config = {}) => api.put(url, data, config),
  patch: (url, data = {}, config = {}) => api.patch(url, data, config),
  delete: (url, config = {}) => api.delete(url, config),

  // File upload helper
  upload: (url, file, onUploadProgress = null) => {
    const formData = new FormData();
    formData.append('file', file);

    return api.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress,
    });
  },

  // Multiple file upload helper
  uploadMultiple: (url, files, onUploadProgress = null) => {
    const formData = new FormData();
    files.forEach((file, index) => {
      formData.append(`files[${index}]`, file);
    });

    return api.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress,
    });
  },
};

export default api;
