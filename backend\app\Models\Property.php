<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use <PERSON><PERSON>\Scout\Searchable;

class Property extends Model
{
    use HasFactory, SoftDeletes, Searchable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'agency_id',
        'title',
        'slug',
        'description',
        'type',
        'listing_type',
        'status',
        'price',
        'currency',
        'price_per_sqft',
        'monthly_rent',
        'security_deposit',
        'maintenance_fee',
        'hoa_fee',
        'bedrooms',
        'bathrooms',
        'total_area',
        'living_area',
        'lot_size',
        'floors',
        'parking_spaces',
        'year_built',
        'year_renovated',
        'address_line_1',
        'address_line_2',
        'city',
        'state',
        'postal_code',
        'country',
        'neighborhood',
        'latitude',
        'longitude',
        'features',
        'amenities',
        'appliances',
        'featured_image',
        'image_gallery',
        'video_url',
        'virtual_tour_url',
        'floor_plan',
        'meta_title',
        'meta_description',
        'keywords',
        'is_featured',
        'is_premium',
        'featured_until',
        'views_count',
        'favorites_count',
        'inquiries_count',
        'average_rating',
        'reviews_count',
        'available_from',
        'is_negotiable',
        'special_conditions',
        'energy_rating',
        'energy_consumption',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'price' => 'decimal:2',
        'price_per_sqft' => 'decimal:2',
        'monthly_rent' => 'decimal:2',
        'security_deposit' => 'decimal:2',
        'maintenance_fee' => 'decimal:2',
        'hoa_fee' => 'decimal:2',
        'total_area' => 'decimal:2',
        'living_area' => 'decimal:2',
        'lot_size' => 'decimal:2',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'features' => 'array',
        'amenities' => 'array',
        'appliances' => 'array',
        'image_gallery' => 'array',
        'keywords' => 'array',
        'is_featured' => 'boolean',
        'is_premium' => 'boolean',
        'featured_until' => 'datetime',
        'is_negotiable' => 'boolean',
        'available_from' => 'date',
        'average_rating' => 'decimal:2',
        'energy_consumption' => 'decimal:2',
    ];

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Get the agency that owns the property.
     */
    public function agency()
    {
        return $this->belongsTo(Agency::class);
    }

    /**
     * Get the property images.
     */
    public function images()
    {
        return $this->hasMany(PropertyImage::class)->orderBy('sort_order');
    }

    /**
     * Get the featured image.
     */
    public function featuredImage()
    {
        return $this->hasOne(PropertyImage::class)->where('is_featured', true);
    }

    /**
     * Get the property features (many-to-many).
     */
    public function propertyFeatures()
    {
        return $this->belongsToMany(PropertyFeature::class, 'property_property_feature')
                    ->withPivot('value', 'notes')
                    ->withTimestamps();
    }

    /**
     * Get the property favorites.
     */
    public function favorites()
    {
        return $this->hasMany(Favorite::class);
    }

    /**
     * Get users who favorited this property.
     */
    public function favoritedBy()
    {
        return $this->belongsToMany(User::class, 'favorites')->withTimestamps();
    }

    /**
     * Get the property views.
     */
    public function views()
    {
        return $this->hasMany(PropertyView::class);
    }

    /**
     * Get the property reviews.
     */
    public function reviews()
    {
        return $this->hasMany(PropertyReview::class);
    }

    /**
     * Get messages related to this property.
     */
    public function messages()
    {
        return $this->hasMany(Message::class);
    }

    /**
     * Scope to filter by status.
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get active properties.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get featured properties.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true)
                    ->where(function ($q) {
                        $q->whereNull('featured_until')
                          ->orWhere('featured_until', '>', now());
                    });
    }

    /**
     * Scope to get premium properties.
     */
    public function scopePremium($query)
    {
        return $query->where('is_premium', true);
    }

    /**
     * Scope to filter by type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to filter by listing type.
     */
    public function scopeByListingType($query, string $listingType)
    {
        return $query->where('listing_type', $listingType);
    }

    /**
     * Scope to filter by price range.
     */
    public function scopeByPriceRange($query, float $minPrice = null, float $maxPrice = null)
    {
        if ($minPrice !== null) {
            $query->where('price', '>=', $minPrice);
        }

        if ($maxPrice !== null) {
            $query->where('price', '<=', $maxPrice);
        }

        return $query;
    }

    /**
     * Scope to filter by bedrooms.
     */
    public function scopeByBedrooms($query, int $bedrooms)
    {
        return $query->where('bedrooms', '>=', $bedrooms);
    }

    /**
     * Scope to filter by bathrooms.
     */
    public function scopeByBathrooms($query, int $bathrooms)
    {
        return $query->where('bathrooms', '>=', $bathrooms);
    }

    /**
     * Scope to filter by location.
     */
    public function scopeByLocation($query, string $city = null, string $state = null, string $country = null)
    {
        if ($city) {
            $query->where('city', 'like', '%' . $city . '%');
        }

        if ($state) {
            $query->where('state', 'like', '%' . $state . '%');
        }

        if ($country) {
            $query->where('country', 'like', '%' . $country . '%');
        }

        return $query;
    }

    /**
     * Scope to filter by area within radius.
     */
    public function scopeWithinRadius($query, float $latitude, float $longitude, float $radiusKm)
    {
        $earthRadius = 6371; // Earth's radius in kilometers

        return $query->selectRaw("
            *, (
                {$earthRadius} * acos(
                    cos(radians(?)) *
                    cos(radians(latitude)) *
                    cos(radians(longitude) - radians(?)) +
                    sin(radians(?)) *
                    sin(radians(latitude))
                )
            ) AS distance
        ", [$latitude, $longitude, $latitude])
        ->having('distance', '<=', $radiusKm)
        ->orderBy('distance');
    }

    /**
     * Get the formatted price.
     */
    public function getFormattedPriceAttribute(): string
    {
        return '$' . number_format($this->price, 2);
    }

    /**
     * Get the full address.
     */
    public function getFullAddressAttribute(): string
    {
        $parts = array_filter([
            $this->address_line_1,
            $this->address_line_2,
            $this->city,
            $this->state,
            $this->postal_code,
            $this->country,
        ]);

        return implode(', ', $parts);
    }

    /**
     * Check if property is currently featured.
     */
    public function getIsFeaturedActiveAttribute(): bool
    {
        return $this->is_featured &&
               ($this->featured_until === null || $this->featured_until->isFuture());
    }

    /**
     * Get property status badge.
     */
    public function getStatusBadgeAttribute(): array
    {
        $badges = [
            'active' => ['text' => 'Active', 'color' => 'green'],
            'inactive' => ['text' => 'Inactive', 'color' => 'gray'],
            'sold' => ['text' => 'Sold', 'color' => 'blue'],
            'rented' => ['text' => 'Rented', 'color' => 'purple'],
            'pending' => ['text' => 'Pending', 'color' => 'yellow'],
            'draft' => ['text' => 'Draft', 'color' => 'gray'],
        ];

        return $badges[$this->status] ?? ['text' => 'Unknown', 'color' => 'gray'];
    }

    /**
     * Check if property has specific feature.
     */
    public function hasFeature(string $feature): bool
    {
        return in_array($feature, $this->features ?? []);
    }

    /**
     * Check if property has specific amenity.
     */
    public function hasAmenity(string $amenity): bool
    {
        return in_array($amenity, $this->amenities ?? []);
    }

    /**
     * Check if property has specific appliance.
     */
    public function hasAppliance(string $appliance): bool
    {
        return in_array($appliance, $this->appliances ?? []);
    }

    /**
     * Increment views count.
     */
    public function incrementViews(): bool
    {
        return $this->increment('views_count');
    }

    /**
     * Increment favorites count.
     */
    public function incrementFavorites(): bool
    {
        return $this->increment('favorites_count');
    }

    /**
     * Decrement favorites count.
     */
    public function decrementFavorites(): bool
    {
        return $this->decrement('favorites_count');
    }

    /**
     * Increment inquiries count.
     */
    public function incrementInquiries(): bool
    {
        return $this->increment('inquiries_count');
    }

    /**
     * Get searchable data for Scout.
     */
    public function toSearchableArray(): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'type' => $this->type,
            'listing_type' => $this->listing_type,
            'price' => $this->price,
            'bedrooms' => $this->bedrooms,
            'bathrooms' => $this->bathrooms,
            'city' => $this->city,
            'state' => $this->state,
            'country' => $this->country,
            'neighborhood' => $this->neighborhood,
            'features' => $this->features,
            'amenities' => $this->amenities,
            'keywords' => $this->keywords,
            'status' => $this->status,
        ];
    }
}
