<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Agency extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'name',
        'slug',
        'description',
        'logo',
        'banner_image',
        'license_number',
        'license_expiry',
        'website',
        'phone',
        'email',
        'fax',
        'address_line_1',
        'address_line_2',
        'city',
        'state',
        'postal_code',
        'country',
        'latitude',
        'longitude',
        'business_registration_number',
        'tax_id',
        'service_areas',
        'specializations',
        'facebook_url',
        'twitter_url',
        'instagram_url',
        'linkedin_url',
        'total_properties',
        'active_properties',
        'sold_properties',
        'rented_properties',
        'average_rating',
        'total_reviews',
        'status',
        'is_verified',
        'verified_at',
        'is_featured',
        'business_hours',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'license_expiry' => 'date',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'service_areas' => 'array',
        'specializations' => 'array',
        'is_verified' => 'boolean',
        'verified_at' => 'datetime',
        'is_featured' => 'boolean',
        'business_hours' => 'array',
        'average_rating' => 'decimal:2',
    ];

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Get the user that owns the agency.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the properties for this agency.
     */
    public function properties()
    {
        return $this->hasMany(Property::class);
    }

    /**
     * Get active properties for this agency.
     */
    public function activeProperties()
    {
        return $this->hasMany(Property::class)->where('status', 'active');
    }

    /**
     * Scope to get verified agencies.
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope to get featured agencies.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope to get active agencies.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Get the full address.
     */
    public function getFullAddressAttribute(): string
    {
        $parts = array_filter([
            $this->address_line_1,
            $this->address_line_2,
            $this->city,
            $this->state,
            $this->postal_code,
            $this->country,
        ]);

        return implode(', ', $parts);
    }

    /**
     * Get the status badge.
     */
    public function getStatusBadgeAttribute(): array
    {
        $badges = [
            'active' => ['text' => 'Active', 'color' => 'green'],
            'inactive' => ['text' => 'Inactive', 'color' => 'gray'],
            'suspended' => ['text' => 'Suspended', 'color' => 'red'],
            'pending_verification' => ['text' => 'Pending Verification', 'color' => 'yellow'],
        ];

        return $badges[$this->status] ?? ['text' => 'Unknown', 'color' => 'gray'];
    }

    /**
     * Check if agency serves a specific area.
     */
    public function servesArea(string $area): bool
    {
        return in_array($area, $this->service_areas ?? []);
    }

    /**
     * Check if agency specializes in a specific property type.
     */
    public function specializesIn(string $type): bool
    {
        return in_array($type, $this->specializations ?? []);
    }

    /**
     * Get the success rate percentage.
     */
    public function getSuccessRateAttribute(): float
    {
        if ($this->total_properties === 0) {
            return 0;
        }

        $successfulProperties = $this->sold_properties + $this->rented_properties;
        return round(($successfulProperties / $this->total_properties) * 100, 2);
    }

    /**
     * Update property statistics.
     */
    public function updatePropertyStats(): void
    {
        $this->update([
            'total_properties' => $this->properties()->count(),
            'active_properties' => $this->properties()->where('status', 'active')->count(),
            'sold_properties' => $this->properties()->where('status', 'sold')->count(),
            'rented_properties' => $this->properties()->where('status', 'rented')->count(),
        ]);
    }
}
