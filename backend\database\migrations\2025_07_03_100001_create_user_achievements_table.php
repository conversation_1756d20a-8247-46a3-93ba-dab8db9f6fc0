<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_achievements', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('achievement_type'); // e.g., 'first_property_saved', 'property_hunter', 'early_adopter'
            $table->string('achievement_name');
            $table->text('achievement_description');
            $table->string('badge_icon')->nullable();
            $table->string('badge_color')->nullable();
            $table->integer('points_awarded')->default(0);
            $table->json('metadata')->nullable(); // Additional data about the achievement
            $table->datetime('earned_at');
            $table->boolean('is_displayed')->default(true); // Whether user wants to display this badge

            $table->timestamps();

            // Unique constraint to prevent duplicate achievements
            $table->unique(['user_id', 'achievement_type']);

            // Indexes
            $table->index('user_id');
            $table->index('achievement_type');
            $table->index('earned_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_achievements');
    }
};
