<?php

namespace Database\Seeders;

use App\Models\PropertyFeature;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PropertyFeatureSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $features = [
            // Interior Features
            ['name' => 'Hardwood Floors', 'slug' => 'hardwood-floors', 'category' => 'interior', 'icon' => 'home', 'sort_order' => 1],
            ['name' => 'Tile Floors', 'slug' => 'tile-floors', 'category' => 'interior', 'icon' => 'grid', 'sort_order' => 2],
            ['name' => 'Carpet', 'slug' => 'carpet', 'category' => 'interior', 'icon' => 'layers', 'sort_order' => 3],
            ['name' => 'Fireplace', 'slug' => 'fireplace', 'category' => 'interior', 'icon' => 'flame', 'sort_order' => 4],
            ['name' => 'Walk-in Closet', 'slug' => 'walk-in-closet', 'category' => 'interior', 'icon' => 'archive', 'sort_order' => 5],
            ['name' => 'High Ceilings', 'slug' => 'high-ceilings', 'category' => 'interior', 'icon' => 'arrow-up', 'sort_order' => 6],
            ['name' => 'Open Floor Plan', 'slug' => 'open-floor-plan', 'category' => 'interior', 'icon' => 'layout', 'sort_order' => 7],
            ['name' => 'Vaulted Ceilings', 'slug' => 'vaulted-ceilings', 'category' => 'interior', 'icon' => 'triangle', 'sort_order' => 8],
            ['name' => 'Built-in Storage', 'slug' => 'built-in-storage', 'category' => 'interior', 'icon' => 'box', 'sort_order' => 9],
            ['name' => 'Crown Molding', 'slug' => 'crown-molding', 'category' => 'interior', 'icon' => 'frame', 'sort_order' => 10],

            // Exterior Features
            ['name' => 'Swimming Pool', 'slug' => 'swimming-pool', 'category' => 'exterior', 'icon' => 'waves', 'sort_order' => 11],
            ['name' => 'Hot Tub/Spa', 'slug' => 'hot-tub-spa', 'category' => 'exterior', 'icon' => 'circle', 'sort_order' => 12],
            ['name' => 'Garden', 'slug' => 'garden', 'category' => 'exterior', 'icon' => 'flower', 'sort_order' => 13],
            ['name' => 'Patio', 'slug' => 'patio', 'category' => 'exterior', 'icon' => 'square', 'sort_order' => 14],
            ['name' => 'Deck', 'slug' => 'deck', 'category' => 'exterior', 'icon' => 'grid', 'sort_order' => 15],
            ['name' => 'Balcony', 'slug' => 'balcony', 'category' => 'exterior', 'icon' => 'minimize', 'sort_order' => 16],
            ['name' => 'Garage', 'slug' => 'garage', 'category' => 'exterior', 'icon' => 'car', 'sort_order' => 17],
            ['name' => 'Carport', 'slug' => 'carport', 'category' => 'exterior', 'icon' => 'umbrella', 'sort_order' => 18],
            ['name' => 'Fenced Yard', 'slug' => 'fenced-yard', 'category' => 'exterior', 'icon' => 'shield', 'sort_order' => 19],
            ['name' => 'Sprinkler System', 'slug' => 'sprinkler-system', 'category' => 'exterior', 'icon' => 'droplets', 'sort_order' => 20],

            // Amenities
            ['name' => 'Gym/Fitness Center', 'slug' => 'gym-fitness-center', 'category' => 'amenity', 'icon' => 'dumbbell', 'sort_order' => 21],
            ['name' => 'Clubhouse', 'slug' => 'clubhouse', 'category' => 'amenity', 'icon' => 'users', 'sort_order' => 22],
            ['name' => 'Tennis Court', 'slug' => 'tennis-court', 'category' => 'amenity', 'icon' => 'circle', 'sort_order' => 23],
            ['name' => 'Basketball Court', 'slug' => 'basketball-court', 'category' => 'amenity', 'icon' => 'circle', 'sort_order' => 24],
            ['name' => 'Playground', 'slug' => 'playground', 'category' => 'amenity', 'icon' => 'smile', 'sort_order' => 25],
            ['name' => 'Business Center', 'slug' => 'business-center', 'category' => 'amenity', 'icon' => 'briefcase', 'sort_order' => 26],
            ['name' => 'Concierge', 'slug' => 'concierge', 'category' => 'amenity', 'icon' => 'user-check', 'sort_order' => 27],
            ['name' => 'Doorman', 'slug' => 'doorman', 'category' => 'amenity', 'icon' => 'user', 'sort_order' => 28],
            ['name' => 'Elevator', 'slug' => 'elevator', 'category' => 'amenity', 'icon' => 'arrow-up-down', 'sort_order' => 29],
            ['name' => 'Laundry Facility', 'slug' => 'laundry-facility', 'category' => 'amenity', 'icon' => 'washing-machine', 'sort_order' => 30],

            // Appliances
            ['name' => 'Dishwasher', 'slug' => 'dishwasher', 'category' => 'appliance', 'icon' => 'square', 'sort_order' => 31],
            ['name' => 'Refrigerator', 'slug' => 'refrigerator', 'category' => 'appliance', 'icon' => 'refrigerator', 'sort_order' => 32],
            ['name' => 'Washer/Dryer', 'slug' => 'washer-dryer', 'category' => 'appliance', 'icon' => 'washing-machine', 'sort_order' => 33],
            ['name' => 'Microwave', 'slug' => 'microwave', 'category' => 'appliance', 'icon' => 'square', 'sort_order' => 34],
            ['name' => 'Garbage Disposal', 'slug' => 'garbage-disposal', 'category' => 'appliance', 'icon' => 'trash', 'sort_order' => 35],
            ['name' => 'Range/Oven', 'slug' => 'range-oven', 'category' => 'appliance', 'icon' => 'flame', 'sort_order' => 36],
            ['name' => 'Wine Cooler', 'slug' => 'wine-cooler', 'category' => 'appliance', 'icon' => 'wine', 'sort_order' => 37],
            ['name' => 'Ice Maker', 'slug' => 'ice-maker', 'category' => 'appliance', 'icon' => 'snowflake', 'sort_order' => 38],

            // Security Features
            ['name' => 'Security System', 'slug' => 'security-system', 'category' => 'security', 'icon' => 'shield', 'sort_order' => 39],
            ['name' => 'Gated Community', 'slug' => 'gated-community', 'category' => 'security', 'icon' => 'lock', 'sort_order' => 40],
            ['name' => 'Security Guard', 'slug' => 'security-guard', 'category' => 'security', 'icon' => 'user-shield', 'sort_order' => 41],
            ['name' => 'Intercom System', 'slug' => 'intercom-system', 'category' => 'security', 'icon' => 'phone', 'sort_order' => 42],
            ['name' => 'Video Surveillance', 'slug' => 'video-surveillance', 'category' => 'security', 'icon' => 'video', 'sort_order' => 43],
            ['name' => 'Keyless Entry', 'slug' => 'keyless-entry', 'category' => 'security', 'icon' => 'key', 'sort_order' => 44],

            // Energy Features
            ['name' => 'Solar Panels', 'slug' => 'solar-panels', 'category' => 'energy', 'icon' => 'sun', 'sort_order' => 45],
            ['name' => 'Energy Efficient Windows', 'slug' => 'energy-efficient-windows', 'category' => 'energy', 'icon' => 'square', 'sort_order' => 46],
            ['name' => 'Smart Thermostat', 'slug' => 'smart-thermostat', 'category' => 'energy', 'icon' => 'thermometer', 'sort_order' => 47],
            ['name' => 'LED Lighting', 'slug' => 'led-lighting', 'category' => 'energy', 'icon' => 'lightbulb', 'sort_order' => 48],
            ['name' => 'Energy Star Appliances', 'slug' => 'energy-star-appliances', 'category' => 'energy', 'icon' => 'star', 'sort_order' => 49],

            // Accessibility Features
            ['name' => 'Wheelchair Accessible', 'slug' => 'wheelchair-accessible', 'category' => 'accessibility', 'icon' => 'accessibility', 'sort_order' => 50],
            ['name' => 'Grab Bars', 'slug' => 'grab-bars', 'category' => 'accessibility', 'icon' => 'minus', 'sort_order' => 51],
            ['name' => 'Wide Doorways', 'slug' => 'wide-doorways', 'category' => 'accessibility', 'icon' => 'door-open', 'sort_order' => 52],
            ['name' => 'Ramp Access', 'slug' => 'ramp-access', 'category' => 'accessibility', 'icon' => 'trending-up', 'sort_order' => 53],
        ];

        foreach ($features as $feature) {
            PropertyFeature::create([
                'name' => $feature['name'],
                'slug' => $feature['slug'],
                'description' => "Property feature: {$feature['name']}",
                'icon' => $feature['icon'],
                'category' => $feature['category'],
                'is_active' => true,
                'sort_order' => $feature['sort_order'],
            ]);
        }

        $this->command->info('Property features created successfully!');
    }
}
