import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import {
  HomeIcon,
  HeartIcon,
  BellIcon,
  ChartBarIcon,
  UserIcon,
  CogIcon,
  EyeIcon,
  MapPinIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  ClockIcon,
  StarIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';

const UserDashboard = () => {
  const { user } = useAuth();
  const [stats] = useState({
    savedProperties: 12,
    propertyViews: 48,
    searchAlerts: 3,
    profileViews: 24,
    favoriteAgencies: 5,
    recentSearches: 15
  });

  const quickStats = [
    {
      name: 'Saved Properties',
      value: stats.savedProperties.toString(),
      icon: HeartIcon,
      color: 'bg-red-500',
      change: '+2 this week',
      changeType: 'positive'
    },
    {
      name: 'Property Views',
      value: stats.propertyViews.toString(),
      icon: EyeIcon,
      color: 'bg-blue-500',
      change: '+12 this week',
      changeType: 'positive'
    },
    {
      name: 'Search Alerts',
      value: stats.searchAlerts.toString(),
      icon: BellIcon,
      color: 'bg-yellow-500',
      change: '1 new match',
      changeType: 'positive'
    },
    {
      name: 'Profile Views',
      value: stats.profileViews.toString(),
      icon: UserIcon,
      color: 'bg-green-500',
      change: '+6 this week',
      changeType: 'positive'
    }
  ];

  const quickActions = [
    {
      name: 'Browse Properties',
      description: 'Find your dream home',
      href: '/properties',
      icon: HomeIcon,
      color: 'bg-blue-500'
    },
    {
      name: 'Saved Properties',
      description: 'View your favorites',
      href: '/user/saved-properties',
      icon: HeartIcon,
      color: 'bg-red-500'
    },
    {
      name: 'Search Alerts',
      description: 'Manage your alerts',
      href: '/user/alerts',
      icon: BellIcon,
      color: 'bg-yellow-500'
    },
    {
      name: 'Profile Settings',
      description: 'Update your information',
      href: '/user/profile',
      icon: CogIcon,
      color: 'bg-gray-500'
    }
  ];

  const savedProperties = [
    {
      id: 1,
      title: 'Modern Downtown Condo',
      location: 'Downtown, City Center',
      price: '$450,000',
      bedrooms: 2,
      bathrooms: 2,
      sqft: 1200,
      image: '/api/placeholder/300/200',
      agency: 'Premium Realty',
      savedDate: '2 days ago'
    },
    {
      id: 2,
      title: 'Family Home in Suburbs',
      location: 'Suburban Heights',
      price: '$650,000',
      bedrooms: 4,
      bathrooms: 3,
      sqft: 2400,
      image: '/api/placeholder/300/200',
      agency: 'Family Homes Inc',
      savedDate: '1 week ago'
    },
    {
      id: 3,
      title: 'Luxury Penthouse',
      location: 'Uptown District',
      price: '$1,200,000',
      bedrooms: 3,
      bathrooms: 3,
      sqft: 1800,
      image: '/api/placeholder/300/200',
      agency: 'Luxury Living',
      savedDate: '2 weeks ago'
    }
  ];

  const recentSearches = [
    {
      id: 1,
      query: '3 bedroom houses under $500k',
      location: 'Downtown area',
      results: 23,
      date: '2 hours ago'
    },
    {
      id: 2,
      query: 'Condos with parking',
      location: 'City Center',
      results: 15,
      date: '1 day ago'
    },
    {
      id: 3,
      query: 'Family homes with garden',
      location: 'Suburbs',
      results: 31,
      date: '3 days ago'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center justify-between"
          >
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Welcome back, {user?.first_name}!
              </h1>
              <p className="mt-2 text-gray-600 dark:text-gray-400">
                Here's what's happening with your property search
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <MagnifyingGlassIcon className="h-5 w-5 inline mr-2" />
                Search Properties
              </motion.button>
            </div>
          </motion.div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {quickStats.map((stat, index) => (
            <motion.div
              key={stat.name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {stat.name}
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {stat.value}
                  </p>
                  <p className="text-sm text-green-600">
                    {stat.change}
                  </p>
                </div>
                <div className={`${stat.color} p-3 rounded-lg`}>
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Quick Actions
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {quickActions.map((action, index) => (
              <motion.a
                key={action.name}
                href={action.href}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ scale: 1.02 }}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 hover:shadow-md transition-all duration-200"
              >
                <div className="flex items-center">
                  <div className={`${action.color} p-3 rounded-lg`}>
                    <action.icon className="h-6 w-6 text-white" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                      {action.name}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {action.description}
                    </p>
                  </div>
                </div>
              </motion.a>
            ))}
          </div>
        </div>

        {/* Saved Properties and Recent Searches */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Saved Properties */}
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Saved Properties
            </h2>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
              <div className="p-6">
                <div className="space-y-4">
                  {savedProperties.map((property, index) => (
                    <motion.div
                      key={property.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="flex items-center space-x-4 p-4 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                    >
                      <div className="w-16 h-16 bg-gray-200 dark:bg-gray-600 rounded-lg flex items-center justify-center">
                        <HomeIcon className="h-8 w-8 text-gray-400" />
                      </div>
                      <div className="flex-1">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                          {property.title}
                        </h4>
                        <p className="text-lg font-bold text-blue-600 dark:text-blue-400">
                          {property.price}
                        </p>
                        <div className="flex items-center space-x-4 mt-1">
                          <span className="flex items-center text-xs text-gray-500">
                            <MapPinIcon className="h-3 w-3 mr-1" />
                            {property.location}
                          </span>
                          <span className="text-xs text-gray-500">
                            {property.bedrooms}bd • {property.bathrooms}ba
                          </span>
                        </div>
                        <p className="text-xs text-gray-400 mt-1">
                          Saved {property.savedDate}
                        </p>
                      </div>
                      <div>
                        <HeartIcon className="h-5 w-5 text-red-500 fill-current" />
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Recent Searches */}
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Recent Searches
            </h2>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
              <div className="p-6">
                <div className="space-y-4">
                  {recentSearches.map((search, index) => (
                    <motion.div
                      key={search.id}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="flex items-center justify-between p-4 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                    >
                      <div className="flex-1">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                          {search.query}
                        </h4>
                        <div className="flex items-center space-x-4 mt-1">
                          <span className="flex items-center text-xs text-gray-500">
                            <MapPinIcon className="h-3 w-3 mr-1" />
                            {search.location}
                          </span>
                          <span className="text-xs text-gray-500">
                            {search.results} results
                          </span>
                        </div>
                        <p className="text-xs text-gray-400 mt-1">
                          {search.date}
                        </p>
                      </div>
                      <div>
                        <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                          Search Again
                        </button>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserDashboard;
