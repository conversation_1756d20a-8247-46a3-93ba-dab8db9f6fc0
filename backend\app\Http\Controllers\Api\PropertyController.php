<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Property\CreatePropertyRequest;
use App\Http\Requests\Property\UpdatePropertyRequest;
use App\Models\Property;
use App\Models\PropertyView;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class PropertyController extends Controller
{
    /**
     * Display a listing of properties.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Property::with(['agency', 'images', 'featuredImage']);

            // Filter by status
            if ($request->has('status')) {
                $query->byStatus($request->status);
            } else {
                $query->active(); // Default to active properties
            }

            // Filter by type
            if ($request->has('type')) {
                $query->byType($request->type);
            }

            // Filter by listing type
            if ($request->has('listing_type')) {
                $query->byListingType($request->listing_type);
            }

            // Filter by price range
            if ($request->has('min_price') || $request->has('max_price')) {
                $query->byPriceRange($request->min_price, $request->max_price);
            }

            // Filter by bedrooms
            if ($request->has('bedrooms')) {
                $query->byBedrooms($request->bedrooms);
            }

            // Filter by bathrooms
            if ($request->has('bathrooms')) {
                $query->byBathrooms($request->bathrooms);
            }

            // Filter by location
            if ($request->has('city') || $request->has('state') || $request->has('country')) {
                $query->byLocation($request->city, $request->state, $request->country);
            }

            // Filter by radius
            if ($request->has('latitude') && $request->has('longitude') && $request->has('radius')) {
                $query->withinRadius(
                    $request->latitude,
                    $request->longitude,
                    $request->radius
                );
            }

            // Filter featured properties
            if ($request->boolean('featured_only')) {
                $query->featured();
            }

            // Filter premium properties
            if ($request->boolean('premium_only')) {
                $query->premium();
            }

            // Search by keyword
            if ($request->has('search')) {
                $searchTerm = $request->search;
                $query->where(function ($q) use ($searchTerm) {
                    $q->where('title', 'like', '%' . $searchTerm . '%')
                      ->orWhere('description', 'like', '%' . $searchTerm . '%')
                      ->orWhere('city', 'like', '%' . $searchTerm . '%')
                      ->orWhere('neighborhood', 'like', '%' . $searchTerm . '%');
                });
            }

            // Sorting
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');

            $allowedSorts = ['created_at', 'price', 'bedrooms', 'bathrooms', 'total_area', 'views_count', 'favorites_count'];
            if (in_array($sortBy, $allowedSorts)) {
                $query->orderBy($sortBy, $sortOrder);
            }

            // Pagination
            $perPage = min($request->get('per_page', 12), 50);
            $properties = $query->paginate($perPage);

            // Add computed attributes
            $properties->getCollection()->each(function ($property) {
                $property->append(['formatted_price', 'full_address', 'is_featured_active', 'status_badge']);
            });

            return response()->json([
                'properties' => $properties->items(),
                'pagination' => [
                    'current_page' => $properties->currentPage(),
                    'last_page' => $properties->lastPage(),
                    'per_page' => $properties->perPage(),
                    'total' => $properties->total(),
                    'from' => $properties->firstItem(),
                    'to' => $properties->lastItem(),
                ],
                'filters_applied' => $request->except(['page', 'per_page']),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to fetch properties',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created property.
     */
    public function store(CreatePropertyRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $data = $request->validated();

            // Get the agency for the authenticated user
            $agency = auth()->user()->agency;
            if (!$agency) {
                return response()->json([
                    'message' => 'User does not have an associated agency'
                ], 400);
            }

            $data['agency_id'] = $agency->id;

            // Generate slug if not provided
            if (!isset($data['slug'])) {
                $data['slug'] = Str::slug($data['title']);
            }

            // Ensure slug is unique
            $originalSlug = $data['slug'];
            $counter = 1;
            while (Property::where('slug', $data['slug'])->exists()) {
                $data['slug'] = $originalSlug . '-' . $counter;
                $counter++;
            }

            // Calculate price per sqft if not provided
            if (!isset($data['price_per_sqft']) && isset($data['total_area']) && $data['total_area'] > 0) {
                $data['price_per_sqft'] = $data['price'] / $data['total_area'];
            }

            $property = Property::create($data);

            // Attach property features if provided
            if ($request->has('property_features')) {
                $features = [];
                foreach ($request->property_features as $featureData) {
                    $features[$featureData['id']] = [
                        'value' => $featureData['value'] ?? null,
                        'notes' => $featureData['notes'] ?? null,
                    ];
                }
                $property->propertyFeatures()->attach($features);
            }

            // Update agency statistics
            $agency->updatePropertyStats();

            DB::commit();

            return response()->json([
                'message' => 'Property created successfully',
                'property' => $property->load(['agency', 'images', 'propertyFeatures'])
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Failed to create property',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified property.
     */
    public function show(Property $property, Request $request): JsonResponse
    {
        try {
            // Load relationships
            $property->load([
                'agency',
                'images' => function ($query) {
                    $query->ordered();
                },
                'featuredImage',
                'propertyFeatures',
                'reviews' => function ($query) {
                    $query->where('status', 'approved')
                          ->with('user')
                          ->latest()
                          ->limit(5);
                }
            ]);

            // Track property view
            $this->trackPropertyView($property, $request);

            // Increment views count
            $property->incrementViews();

            // Add computed attributes
            $property->append([
                'formatted_price',
                'full_address',
                'is_featured_active',
                'status_badge'
            ]);

            // Get similar properties
            $similarProperties = $this->getSimilarProperties($property);

            return response()->json([
                'property' => $property,
                'similar_properties' => $similarProperties,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Property not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update the specified property.
     */
    public function update(UpdatePropertyRequest $request, Property $property): JsonResponse
    {
        try {
            // Check if user owns this property
            if (auth()->user()->agency->id !== $property->agency_id && !auth()->user()->can('edit properties')) {
                return response()->json([
                    'message' => 'Unauthorized to edit this property'
                ], 403);
            }

            DB::beginTransaction();

            $data = $request->validated();

            // Update slug if title changed
            if (isset($data['title']) && $data['title'] !== $property->title) {
                $data['slug'] = Str::slug($data['title']);

                // Ensure slug is unique
                $originalSlug = $data['slug'];
                $counter = 1;
                while (Property::where('slug', $data['slug'])->where('id', '!=', $property->id)->exists()) {
                    $data['slug'] = $originalSlug . '-' . $counter;
                    $counter++;
                }
            }

            // Recalculate price per sqft if needed
            if ((isset($data['price']) || isset($data['total_area'])) &&
                ($data['total_area'] ?? $property->total_area) > 0) {
                $price = $data['price'] ?? $property->price;
                $area = $data['total_area'] ?? $property->total_area;
                $data['price_per_sqft'] = $price / $area;
            }

            $property->update($data);

            // Update property features if provided
            if ($request->has('property_features')) {
                $features = [];
                foreach ($request->property_features as $featureData) {
                    $features[$featureData['id']] = [
                        'value' => $featureData['value'] ?? null,
                        'notes' => $featureData['notes'] ?? null,
                    ];
                }
                $property->propertyFeatures()->sync($features);
            }

            // Update agency statistics
            $property->agency->updatePropertyStats();

            DB::commit();

            return response()->json([
                'message' => 'Property updated successfully',
                'property' => $property->fresh(['agency', 'images', 'propertyFeatures'])
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Failed to update property',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified property.
     */
    public function destroy(Property $property): JsonResponse
    {
        try {
            // Check if user owns this property
            if (auth()->user()->agency->id !== $property->agency_id && !auth()->user()->can('delete properties')) {
                return response()->json([
                    'message' => 'Unauthorized to delete this property'
                ], 403);
            }

            DB::beginTransaction();

            $agency = $property->agency;

            // Delete property (soft delete)
            $property->delete();

            // Update agency statistics
            $agency->updatePropertyStats();

            DB::commit();

            return response()->json([
                'message' => 'Property deleted successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Failed to delete property',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Track property view for analytics.
     */
    private function trackPropertyView(Property $property, Request $request): void
    {
        try {
            PropertyView::create([
                'property_id' => $property->id,
                'user_id' => auth()->id(),
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'referrer' => $request->header('referer'),
                'session_id' => session()->getId(),
                'device_info' => [
                    'platform' => $request->header('sec-ch-ua-platform'),
                    'mobile' => $request->header('sec-ch-ua-mobile') === '?1',
                ],
                'view_type' => 'listing',
            ]);
        } catch (\Exception $e) {
            // Log error but don't fail the request
            \Log::warning('Failed to track property view: ' . $e->getMessage());
        }
    }

    /**
     * Get similar properties.
     */
    private function getSimilarProperties(Property $property, int $limit = 4): array
    {
        try {
            $similar = Property::active()
                ->where('id', '!=', $property->id)
                ->where('type', $property->type)
                ->where('listing_type', $property->listing_type)
                ->where('city', $property->city)
                ->byPriceRange(
                    $property->price * 0.8,
                    $property->price * 1.2
                )
                ->with(['agency', 'featuredImage'])
                ->limit($limit)
                ->get();

            $similar->each(function ($prop) {
                $prop->append(['formatted_price', 'full_address']);
            });

            return $similar->toArray();

        } catch (\Exception $e) {
            return [];
        }
    }
}
