<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class PropertyImage extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'property_id',
        'filename',
        'original_name',
        'path',
        'url',
        'thumbnail_url',
        'medium_url',
        'large_url',
        'type',
        'alt_text',
        'caption',
        'sort_order',
        'is_featured',
        'mime_type',
        'file_size',
        'width',
        'height',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_featured' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * Get the property that owns the image.
     */
    public function property()
    {
        return $this->belongsTo(Property::class);
    }

    /**
     * Scope to get images by type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to get featured images.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('created_at');
    }

    /**
     * Get the formatted file size.
     */
    public function getFormattedFileSizeAttribute(): string
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get the image dimensions as string.
     */
    public function getDimensionsAttribute(): string
    {
        if ($this->width && $this->height) {
            return $this->width . ' × ' . $this->height;
        }

        return 'Unknown';
    }

    /**
     * Get the full URL for the image.
     */
    public function getFullUrlAttribute(): string
    {
        if (filter_var($this->url, FILTER_VALIDATE_URL)) {
            return $this->url;
        }

        return Storage::url($this->path);
    }

    /**
     * Get the full URL for the thumbnail.
     */
    public function getFullThumbnailUrlAttribute(): string
    {
        if ($this->thumbnail_url) {
            if (filter_var($this->thumbnail_url, FILTER_VALIDATE_URL)) {
                return $this->thumbnail_url;
            }

            return Storage::url($this->thumbnail_url);
        }

        return $this->full_url;
    }

    /**
     * Get the full URL for the medium size.
     */
    public function getFullMediumUrlAttribute(): string
    {
        if ($this->medium_url) {
            if (filter_var($this->medium_url, FILTER_VALIDATE_URL)) {
                return $this->medium_url;
            }

            return Storage::url($this->medium_url);
        }

        return $this->full_url;
    }

    /**
     * Get the full URL for the large size.
     */
    public function getFullLargeUrlAttribute(): string
    {
        if ($this->large_url) {
            if (filter_var($this->large_url, FILTER_VALIDATE_URL)) {
                return $this->large_url;
            }

            return Storage::url($this->large_url);
        }

        return $this->full_url;
    }

    /**
     * Delete the image file from storage.
     */
    public function deleteFile(): bool
    {
        $deleted = true;

        // Delete main image
        if (Storage::exists($this->path)) {
            $deleted = Storage::delete($this->path) && $deleted;
        }

        // Delete thumbnail
        if ($this->thumbnail_url && Storage::exists($this->thumbnail_url)) {
            $deleted = Storage::delete($this->thumbnail_url) && $deleted;
        }

        // Delete medium size
        if ($this->medium_url && Storage::exists($this->medium_url)) {
            $deleted = Storage::delete($this->medium_url) && $deleted;
        }

        // Delete large size
        if ($this->large_url && Storage::exists($this->large_url)) {
            $deleted = Storage::delete($this->large_url) && $deleted;
        }

        return $deleted;
    }

    /**
     * Set as featured image (unset others).
     */
    public function setAsFeatured(): bool
    {
        // Unset other featured images for this property
        static::where('property_id', $this->property_id)
              ->where('id', '!=', $this->id)
              ->update(['is_featured' => false]);

        // Set this image as featured
        return $this->update(['is_featured' => true]);
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Delete file when model is deleted
        static::deleting(function ($image) {
            $image->deleteFile();
        });
    }
}
