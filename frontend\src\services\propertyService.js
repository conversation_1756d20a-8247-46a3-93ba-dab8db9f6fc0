import api from './api';

const propertyService = {
  // Get all properties with pagination and filters
  getProperties: async (params = {}) => {
    const response = await api.get('/properties', { params });
    return response;
  },

  // Get property by ID
  getPropertyById: async (id) => {
    const response = await api.get(`/properties/${id}`);
    return response;
  },

  // Search properties
  searchProperties: async (searchParams) => {
    const response = await api.post('/properties/search', searchParams);
    return response;
  },

  // Create new property (Agency only)
  createProperty: async (propertyData) => {
    const response = await api.post('/properties', propertyData);
    return response;
  },

  // Update property (Agency only)
  updateProperty: async (id, propertyData) => {
    const response = await api.put(`/properties/${id}`, propertyData);
    return response;
  },

  // Delete property (Agency only)
  deleteProperty: async (id) => {
    const response = await api.delete(`/properties/${id}`);
    return response;
  },

  // Upload property images
  uploadPropertyImages: async (propertyId, images) => {
    const formData = new FormData();
    images.forEach((image, index) => {
      formData.append(`images[${index}]`, image);
    });

    const response = await api.post(`/properties/${propertyId}/images`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response;
  },

  // Delete property image
  deletePropertyImage: async (propertyId, imageId) => {
    const response = await api.delete(`/properties/${propertyId}/images/${imageId}`);
    return response;
  },

  // Get property types
  getPropertyTypes: async () => {
    const response = await api.get('/property-types');
    return response;
  },

  // Get property features
  getPropertyFeatures: async () => {
    const response = await api.get('/property-features');
    return response;
  },

  // Save property to favorites (User only)
  saveToFavorites: async (propertyId) => {
    const response = await api.post(`/properties/${propertyId}/favorite`);
    return response;
  },

  // Remove property from favorites (User only)
  removeFromFavorites: async (propertyId) => {
    const response = await api.delete(`/properties/${propertyId}/favorite`);
    return response;
  },

  // Get user's favorite properties
  getFavoriteProperties: async () => {
    const response = await api.get('/user/favorites');
    return response;
  },

  // Contact property agent
  contactAgent: async (propertyId, message) => {
    const response = await api.post(`/properties/${propertyId}/contact`, {
      message,
    });
    return response;
  },

  // Schedule property viewing
  scheduleViewing: async (propertyId, viewingData) => {
    const response = await api.post(`/properties/${propertyId}/schedule-viewing`, viewingData);
    return response;
  },

  // Get property analytics (Agency only)
  getPropertyAnalytics: async (propertyId) => {
    const response = await api.get(`/properties/${propertyId}/analytics`);
    return response;
  },

  // Get similar properties
  getSimilarProperties: async (propertyId) => {
    const response = await api.get(`/properties/${propertyId}/similar`);
    return response;
  },

  // Get properties by location
  getPropertiesByLocation: async (location) => {
    const response = await api.get('/properties/by-location', {
      params: { location },
    });
    return response;
  },

  // Get property valuation
  getPropertyValuation: async (propertyData) => {
    const response = await api.post('/properties/valuation', propertyData);
    return response;
  },
};

export default propertyService;
