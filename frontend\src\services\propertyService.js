import api from './api';

const propertyService = {
  // Get all properties with pagination and filters
  getProperties: async (params = {}) => {
    const response = await api.get('/properties', { params });
    return response;
  },

  // Get property by slug
  getPropertyBySlug: async (slug) => {
    const response = await api.get(`/properties/${slug}`);
    return response;
  },

  // Advanced property search
  searchProperties: async (searchParams) => {
    const response = await api.get('/properties/search', { params: searchParams });
    return response;
  },

  // Get featured properties
  getFeaturedProperties: async (params = {}) => {
    const response = await api.get('/properties/featured', { params });
    return response;
  },

  // Get similar properties
  getSimilarProperties: async (propertySlug) => {
    const response = await api.get(`/properties/similar/${propertySlug}`);
    return response;
  },

  // Agency property management
  createProperty: async (propertyData) => {
    const response = await api.post('/agency/properties', propertyData);
    return response;
  },

  updateProperty: async (slug, propertyData) => {
    const response = await api.put(`/agency/properties/${slug}`, propertyData);
    return response;
  },

  deleteProperty: async (slug) => {
    const response = await api.delete(`/agency/properties/${slug}`);
    return response;
  },

  // Property image management
  uploadPropertyImages: async (propertySlug, images) => {
    const formData = new FormData();
    images.forEach((image, index) => {
      formData.append(`images[${index}]`, image);
    });

    const response = await api.post(`/agency/properties/${propertySlug}/images`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response;
  },

  updatePropertyImage: async (propertySlug, imageId, imageData) => {
    const response = await api.put(`/agency/properties/${propertySlug}/images/${imageId}`, imageData);
    return response;
  },

  deletePropertyImage: async (propertySlug, imageId) => {
    const response = await api.delete(`/agency/properties/${propertySlug}/images/${imageId}`);
    return response;
  },

  setFeaturedImage: async (propertySlug, imageId) => {
    const response = await api.post(`/agency/properties/${propertySlug}/images/${imageId}/featured`);
    return response;
  },

  // Property filters and metadata
  getPropertyTypes: () => {
    return [
      { value: 'house', label: 'House' },
      { value: 'apartment', label: 'Apartment' },
      { value: 'condo', label: 'Condo' },
      { value: 'townhouse', label: 'Townhouse' },
      { value: 'villa', label: 'Villa' },
      { value: 'studio', label: 'Studio' },
      { value: 'duplex', label: 'Duplex' },
      { value: 'penthouse', label: 'Penthouse' },
      { value: 'land', label: 'Land' },
      { value: 'commercial', label: 'Commercial' },
      { value: 'office', label: 'Office' },
      { value: 'retail', label: 'Retail' },
      { value: 'warehouse', label: 'Warehouse' },
      { value: 'industrial', label: 'Industrial' },
    ];
  },

  getListingTypes: () => {
    return [
      { value: 'sale', label: 'For Sale' },
      { value: 'rent', label: 'For Rent' },
      { value: 'lease', label: 'For Lease' },
    ];
  },

  getPropertyStatuses: () => {
    return [
      { value: 'active', label: 'Active' },
      { value: 'inactive', label: 'Inactive' },
      { value: 'sold', label: 'Sold' },
      { value: 'rented', label: 'Rented' },
      { value: 'pending', label: 'Pending' },
      { value: 'draft', label: 'Draft' },
    ];
  },

  // Price formatting utilities
  formatPrice: (price, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  },

  formatArea: (area, unit = 'sqft') => {
    return `${new Intl.NumberFormat('en-US').format(area)} ${unit}`;
  },

  // Search filters
  buildSearchParams: (filters) => {
    const params = {};

    // Basic filters
    if (filters.search) params.search = filters.search;
    if (filters.type) params.type = filters.type;
    if (filters.listing_type) params.listing_type = filters.listing_type;
    if (filters.status) params.status = filters.status;

    // Price range
    if (filters.min_price) params.min_price = filters.min_price;
    if (filters.max_price) params.max_price = filters.max_price;

    // Property details
    if (filters.bedrooms) params.bedrooms = filters.bedrooms;
    if (filters.bathrooms) params.bathrooms = filters.bathrooms;

    // Location
    if (filters.city) params.city = filters.city;
    if (filters.state) params.state = filters.state;
    if (filters.country) params.country = filters.country;

    // Location radius search
    if (filters.latitude && filters.longitude && filters.radius) {
      params.latitude = filters.latitude;
      params.longitude = filters.longitude;
      params.radius = filters.radius;
    }

    // Special filters
    if (filters.featured_only) params.featured_only = true;
    if (filters.premium_only) params.premium_only = true;

    // Sorting
    if (filters.sort_by) params.sort_by = filters.sort_by;
    if (filters.sort_order) params.sort_order = filters.sort_order;

    // Pagination
    if (filters.page) params.page = filters.page;
    if (filters.per_page) params.per_page = filters.per_page;

    return params;
  },

  // Default search filters
  getDefaultFilters: () => {
    return {
      search: '',
      type: '',
      listing_type: '',
      min_price: '',
      max_price: '',
      bedrooms: '',
      bathrooms: '',
      city: '',
      state: '',
      country: '',
      featured_only: false,
      premium_only: false,
      sort_by: 'created_at',
      sort_order: 'desc',
      page: 1,
      per_page: 12,
    };
  },

  // Property validation
  validatePropertyData: (propertyData) => {
    const errors = {};

    if (!propertyData.title?.trim()) {
      errors.title = 'Property title is required';
    }

    if (!propertyData.description?.trim()) {
      errors.description = 'Property description is required';
    }

    if (!propertyData.type) {
      errors.type = 'Property type is required';
    }

    if (!propertyData.listing_type) {
      errors.listing_type = 'Listing type is required';
    }

    if (!propertyData.price || propertyData.price <= 0) {
      errors.price = 'Valid price is required';
    }

    if (!propertyData.address_line_1?.trim()) {
      errors.address_line_1 = 'Street address is required';
    }

    if (!propertyData.city?.trim()) {
      errors.city = 'City is required';
    }

    if (!propertyData.state?.trim()) {
      errors.state = 'State is required';
    }

    if (!propertyData.postal_code?.trim()) {
      errors.postal_code = 'Postal code is required';
    }

    if (!propertyData.country?.trim()) {
      errors.country = 'Country is required';
    }

    if (!propertyData.latitude || !propertyData.longitude) {
      errors.location = 'Property location coordinates are required';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
    };
  },
};

export default propertyService;
