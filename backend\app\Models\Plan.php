<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Plan extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'slug',
        'description',
        'type',
        'price',
        'billing_cycle',
        'billing_cycle_months',
        'features',
        'property_listing_limit',
        'featured_listing_limit',
        'photo_limit_per_property',
        'video_limit_per_property',
        'virtual_tour_enabled',
        'analytics_enabled',
        'lead_management_enabled',
        'priority_support_enabled',
        'api_access_enabled',
        'saved_properties_limit',
        'search_alerts_limit',
        'advanced_search_enabled',
        'property_recommendations_enabled',
        'market_insights_enabled',
        'is_popular',
        'is_active',
        'sort_order',
        'stripe_price_id',
        'color_scheme',
        'icon',
        'has_trial',
        'trial_days',
        'setup_fee',
        'discount_percentage',
        'discount_valid_until',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'features' => 'array',
        'virtual_tour_enabled' => 'boolean',
        'analytics_enabled' => 'boolean',
        'lead_management_enabled' => 'boolean',
        'priority_support_enabled' => 'boolean',
        'api_access_enabled' => 'boolean',
        'advanced_search_enabled' => 'boolean',
        'property_recommendations_enabled' => 'boolean',
        'market_insights_enabled' => 'boolean',
        'is_popular' => 'boolean',
        'is_active' => 'boolean',
        'has_trial' => 'boolean',
        'price' => 'decimal:2',
        'setup_fee' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'discount_valid_until' => 'date',
    ];

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Get the subscriptions for this plan.
     */
    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Get active subscriptions for this plan.
     */
    public function activeSubscriptions()
    {
        return $this->hasMany(Subscription::class)->where('status', 'active');
    }

    /**
     * Scope to get plans by type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to get active plans.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get popular plans.
     */
    public function scopePopular($query)
    {
        return $query->where('is_popular', true);
    }

    /**
     * Scope to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('price');
    }

    /**
     * Get the formatted price.
     */
    public function getFormattedPriceAttribute(): string
    {
        return '$' . number_format($this->price, 2);
    }

    /**
     * Get the monthly price (convert from other billing cycles).
     */
    public function getMonthlyPriceAttribute(): float
    {
        return round($this->price / $this->billing_cycle_months, 2);
    }

    /**
     * Get the yearly price (convert to yearly).
     */
    public function getYearlyPriceAttribute(): float
    {
        return round($this->monthly_price * 12, 2);
    }

    /**
     * Check if plan has a specific feature.
     */
    public function hasFeature(string $feature): bool
    {
        return in_array($feature, $this->features ?? []);
    }

    /**
     * Get the effective price after discount.
     */
    public function getEffectivePriceAttribute(): float
    {
        if ($this->discount_percentage && $this->discount_valid_until && $this->discount_valid_until->isFuture()) {
            return $this->price * (1 - ($this->discount_percentage / 100));
        }

        return $this->price;
    }

    /**
     * Check if plan is currently discounted.
     */
    public function getIsDiscountedAttribute(): bool
    {
        return $this->discount_percentage > 0 &&
               $this->discount_valid_until &&
               $this->discount_valid_until->isFuture();
    }

    /**
     * Get savings amount from discount.
     */
    public function getSavingsAmountAttribute(): float
    {
        if ($this->is_discounted) {
            return $this->price - $this->effective_price;
        }

        return 0;
    }

    /**
     * Check if plan is suitable for a specific user type.
     */
    public function isSuitableFor(string $userType): bool
    {
        return $this->type === $userType || $this->type === 'both';
    }

    /**
     * Get plan limits as an array.
     */
    public function getLimitsAttribute(): array
    {
        return [
            'property_listings' => $this->property_listing_limit,
            'featured_listings' => $this->featured_listing_limit,
            'photos_per_property' => $this->photo_limit_per_property,
            'videos_per_property' => $this->video_limit_per_property,
            'saved_properties' => $this->saved_properties_limit,
            'search_alerts' => $this->search_alerts_limit,
        ];
    }

    /**
     * Get plan capabilities as an array.
     */
    public function getCapabilitiesAttribute(): array
    {
        return [
            'virtual_tours' => $this->virtual_tour_enabled,
            'analytics' => $this->analytics_enabled,
            'lead_management' => $this->lead_management_enabled,
            'priority_support' => $this->priority_support_enabled,
            'api_access' => $this->api_access_enabled,
            'advanced_search' => $this->advanced_search_enabled,
            'recommendations' => $this->property_recommendations_enabled,
            'market_insights' => $this->market_insights_enabled,
        ];
    }
}
