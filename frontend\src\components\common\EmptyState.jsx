import React from 'react';
import { motion } from 'framer-motion';
import { 
  HomeIcon,
  MagnifyingGlassIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';

const EmptyState = ({
  type = 'default',
  title,
  description,
  actionText,
  onAction,
  icon: CustomIcon,
  className = ''
}) => {
  const getDefaultIcon = () => {
    switch (type) {
      case 'search':
        return MagnifyingGlassIcon;
      case 'properties':
        return HomeIcon;
      case 'error':
        return ExclamationTriangleIcon;
      case 'info':
        return InformationCircleIcon;
      default:
        return HomeIcon;
    }
  };

  const getDefaultContent = () => {
    switch (type) {
      case 'search':
        return {
          title: 'No results found',
          description: 'Try adjusting your search criteria or browse all properties.'
        };
      case 'properties':
        return {
          title: 'No properties available',
          description: 'There are currently no properties to display.'
        };
      case 'error':
        return {
          title: 'Something went wrong',
          description: 'We encountered an error while loading the content.'
        };
      default:
        return {
          title: 'Nothing to show',
          description: 'There is no content available at the moment.'
        };
    }
  };

  const Icon = CustomIcon || getDefaultIcon();
  const defaultContent = getDefaultContent();
  const finalTitle = title || defaultContent.title;
  const finalDescription = description || defaultContent.description;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={`text-center py-12 px-4 ${className}`}
    >
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="mx-auto mb-6"
      >
        <div className="w-24 h-24 mx-auto bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
          <Icon className="h-12 w-12 text-gray-400 dark:text-gray-500" />
        </div>
      </motion.div>

      <motion.h3
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="text-xl font-semibold text-gray-900 dark:text-white mb-2"
      >
        {finalTitle}
      </motion.h3>

      <motion.p
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.4 }}
        className="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto"
      >
        {finalDescription}
      </motion.p>

      {actionText && onAction && (
        <motion.button
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          onClick={onAction}
          className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 
                     transition-colors duration-200 font-medium"
        >
          {actionText}
        </motion.button>
      )}
    </motion.div>
  );
};

export default EmptyState;
