<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('properties', function (Blueprint $table) {
            $table->id();
            $table->foreignId('agency_id')->constrained()->onDelete('cascade');
            $table->string('title');
            $table->string('slug')->unique();
            $table->text('description');
            $table->enum('type', ['house', 'apartment', 'condo', 'townhouse', 'villa', 'studio', 'duplex', 'penthouse', 'land', 'commercial', 'office', 'retail', 'warehouse', 'industrial']);
            $table->enum('listing_type', ['sale', 'rent', 'lease']);
            $table->enum('status', ['active', 'inactive', 'sold', 'rented', 'pending', 'draft'])->default('draft');

            // Pricing
            $table->decimal('price', 15, 2);
            $table->string('currency', 3)->default('USD');
            $table->decimal('price_per_sqft', 10, 2)->nullable();
            $table->decimal('monthly_rent', 10, 2)->nullable(); // For rental properties
            $table->decimal('security_deposit', 10, 2)->nullable(); // For rental properties
            $table->decimal('maintenance_fee', 10, 2)->nullable();
            $table->decimal('hoa_fee', 10, 2)->nullable(); // Homeowners Association fee

            // Property details
            $table->integer('bedrooms')->nullable();
            $table->integer('bathrooms')->nullable();
            $table->decimal('total_area', 10, 2)->nullable(); // in square feet
            $table->decimal('living_area', 10, 2)->nullable();
            $table->decimal('lot_size', 10, 2)->nullable();
            $table->integer('floors')->nullable();
            $table->integer('parking_spaces')->nullable();
            $table->integer('year_built')->nullable();
            $table->integer('year_renovated')->nullable();

            // Address information
            $table->string('address_line_1');
            $table->string('address_line_2')->nullable();
            $table->string('city');
            $table->string('state');
            $table->string('postal_code');
            $table->string('country');
            $table->string('neighborhood')->nullable();
            $table->decimal('latitude', 10, 8);
            $table->decimal('longitude', 11, 8);

            // Property features (JSON array)
            $table->json('features')->nullable(); // e.g., ["pool", "garage", "garden", "balcony"]
            $table->json('amenities')->nullable(); // e.g., ["gym", "security", "elevator"]
            $table->json('appliances')->nullable(); // e.g., ["dishwasher", "washer", "dryer"]

            // Media
            $table->string('featured_image')->nullable();
            $table->json('image_gallery')->nullable();
            $table->string('video_url')->nullable();
            $table->string('virtual_tour_url')->nullable();
            $table->string('floor_plan')->nullable();

            // SEO and marketing
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->json('keywords')->nullable();
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_premium')->default(false);
            $table->datetime('featured_until')->nullable();

            // Statistics
            $table->integer('views_count')->default(0);
            $table->integer('favorites_count')->default(0);
            $table->integer('inquiries_count')->default(0);
            $table->decimal('average_rating', 3, 2)->default(0);
            $table->integer('reviews_count')->default(0);

            // Availability
            $table->date('available_from')->nullable();
            $table->boolean('is_negotiable')->default(false);
            $table->text('special_conditions')->nullable();

            // Energy efficiency
            $table->string('energy_rating')->nullable(); // A, B, C, D, E, F, G
            $table->decimal('energy_consumption', 8, 2)->nullable(); // kWh/m²/year

            $table->timestamps();
            $table->softDeletes();

            // Indexes for better performance
            $table->index(['agency_id', 'status']);
            $table->index(['type', 'listing_type', 'status']);
            $table->index(['city', 'state']);
            $table->index(['latitude', 'longitude']);
            $table->index(['price', 'listing_type']);
            $table->index(['bedrooms', 'bathrooms']);
            $table->index(['is_featured', 'status']);
            $table->index('available_from');
            $table->fullText(['title', 'description']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('properties');
    }
};
