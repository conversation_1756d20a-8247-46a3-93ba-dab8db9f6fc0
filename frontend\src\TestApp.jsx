import React from 'react';

function TestApp() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          🏠 RealEstate Platform
        </h1>
        <p className="text-xl text-gray-600 mb-8">
          System is working! React and Tailwind CSS are loaded.
        </p>
        <div className="bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto">
          <h2 className="text-2xl font-semibold mb-4">System Status</h2>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Frontend:</span>
              <span className="text-green-600 font-semibold">✅ Running</span>
            </div>
            <div className="flex justify-between">
              <span>Tailwind CSS:</span>
              <span className="text-green-600 font-semibold">✅ Working</span>
            </div>
            <div className="flex justify-between">
              <span>React:</span>
              <span className="text-green-600 font-semibold">✅ Working</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default TestApp;
