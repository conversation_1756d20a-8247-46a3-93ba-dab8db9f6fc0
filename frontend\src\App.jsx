import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Provider } from 'react-redux';
import { store } from './store';
import { Toaster } from 'react-hot-toast';

// Layout Components
import MainLayout from './components/layout/MainLayout';
import AuthLayout from './components/layout/AuthLayout';

// Public Pages
import LandingPage from './pages/public/LandingPage';
import Properties from './pages/Properties';
import PropertyDetail from './pages/PropertyDetail';
import AboutUs from './pages/public/AboutUs';
import ContactUs from './pages/public/ContactUs';

// Auth Pages
import Login from './pages/auth/Login';
import Register from './pages/auth/Register';
import ForgotPassword from './pages/auth/ForgotPassword';
import ResetPassword from './pages/auth/ResetPassword';

// User Pages
import UserDashboard from './pages/user/UserDashboard';
import UserProfile from './pages/user/UserProfile';
import SavedProperties from './pages/user/SavedProperties';
import UserSubscriptions from './pages/user/UserSubscriptions';

// Agency Pages
import AgencyDashboard from './pages/agency/AgencyDashboard';
import AgencyProperties from './pages/agency/AgencyProperties';
import AgencyLeads from './pages/agency/AgencyLeads';
import AgencyAnalytics from './pages/agency/AgencyAnalytics';

// Admin Pages
import AdminDashboard from './pages/admin/AdminDashboard';
import AdminUsers from './pages/admin/AdminUsers';
import AdminAgencies from './pages/admin/AdminAgencies';
import AdminProperties from './pages/admin/AdminProperties';
import AdminPlans from './pages/admin/AdminPlans';

// Protected Route Component
import ProtectedRoute from './components/auth/ProtectedRoute';

// Auth Context
import { AuthProvider } from './contexts/AuthContext';

// Theme initialization
import { useSelector, useDispatch } from 'react-redux';
import { setTheme } from './store/slices/uiSlice';

function App() {
  const dispatch = useDispatch();
  const theme = useSelector((state) => state.ui.theme);

  useEffect(() => {
    // Initialize theme
    const savedTheme = localStorage.getItem('theme') || 'light';
    dispatch(setTheme(savedTheme));

    if (savedTheme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [dispatch]);

  return (
    <Provider store={store}>
      <AuthProvider>
        <Router>
          <div className={`min-h-screen transition-colors duration-300 ${theme === 'dark' ? 'dark bg-gray-900' : 'bg-gray-50'
            }`}>
            <Routes>
              {/* Public Routes */}
              <Route path="/" element={<MainLayout />}>
                <Route index element={<LandingPage />} />
                <Route path="properties" element={<Properties />} />
                <Route path="properties/:slug" element={<PropertyDetail />} />
                <Route path="about" element={<AboutUs />} />
                <Route path="contact" element={<ContactUs />} />
              </Route>

              {/* Auth Routes */}
              <Route path="/auth" element={<AuthLayout />}>
                <Route path="login" element={<Login />} />
                <Route path="register" element={<Register />} />
                <Route path="forgot-password" element={<ForgotPassword />} />
                <Route path="reset-password" element={<ResetPassword />} />
              </Route>

              {/* User Protected Routes */}
              <Route path="/user" element={
                <ProtectedRoute allowedRoles={['user']}>
                  <MainLayout />
                </ProtectedRoute>
              }>
                <Route path="dashboard" element={<UserDashboard />} />
                <Route path="profile" element={<UserProfile />} />
                <Route path="saved-properties" element={<SavedProperties />} />
                <Route path="subscriptions" element={<UserSubscriptions />} />
              </Route>

              {/* Agency Protected Routes */}
              <Route path="/agency" element={
                <ProtectedRoute allowedRoles={['agency']}>
                  <MainLayout />
                </ProtectedRoute>
              }>
                <Route path="dashboard" element={<AgencyDashboard />} />
                <Route path="properties" element={<AgencyProperties />} />
                <Route path="leads" element={<AgencyLeads />} />
                <Route path="analytics" element={<AgencyAnalytics />} />
              </Route>

              {/* Admin Protected Routes */}
              <Route path="/admin" element={
                <ProtectedRoute allowedRoles={['admin']}>
                  <MainLayout />
                </ProtectedRoute>
              }>
                <Route path="dashboard" element={<AdminDashboard />} />
                <Route path="users" element={<AdminUsers />} />
                <Route path="agencies" element={<AdminAgencies />} />
                <Route path="properties" element={<AdminProperties />} />
                <Route path="plans" element={<AdminPlans />} />
              </Route>

              {/* Fallback Routes */}
              <Route path="/login" element={<Navigate to="/auth/login" replace />} />
              <Route path="/register" element={<Navigate to="/auth/register" replace />} />
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>

            {/* Global Toast Notifications */}
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                style: {
                  background: theme === 'dark' ? '#374151' : '#ffffff',
                  color: theme === 'dark' ? '#ffffff' : '#374151',
                  border: `1px solid ${theme === 'dark' ? '#4B5563' : '#E5E7EB'}`,
                },
              }}
            />
          </div>
        </Router>
      </AuthProvider>
    </Provider>
  );
}

export default App;
