import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  AdjustmentsHorizontalIcon,
  Squares2X2Icon,
  ListBulletIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline';
import PropertyCard from './PropertyCard';
import PropertyListItem from './PropertyListItem';
import LoadingSpinner from '../common/LoadingSpinner';
import EmptyState from '../common/EmptyState';

const PropertyGrid = ({ 
  properties = [], 
  loading = false, 
  pagination = null,
  onPageChange,
  onFavoriteToggle,
  onShare,
  className = '',
  showFilters = true,
  defaultView = 'grid'
}) => {
  const [viewMode, setViewMode] = useState(defaultView);
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState('desc');

  const sortOptions = [
    { value: 'created_at', label: 'Newest First' },
    { value: 'price', label: 'Price' },
    { value: 'bedrooms', label: 'Bedrooms' },
    { value: 'bathrooms', label: 'Bathrooms' },
    { value: 'total_area', label: 'Size' },
    { value: 'views_count', label: 'Most Viewed' },
    { value: 'favorites_count', label: 'Most Liked' }
  ];

  const handleSortChange = (newSortBy) => {
    if (newSortBy === sortBy) {
      // Toggle sort order if same field
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(newSortBy);
      setSortOrder('desc');
    }
  };

  const handlePageChange = (page) => {
    if (onPageChange) {
      onPageChange(page);
    }
    // Scroll to top of results
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const renderPagination = () => {
    if (!pagination || pagination.last_page <= 1) return null;

    const { current_page, last_page } = pagination;
    const pages = [];
    const maxVisiblePages = 5;
    
    let startPage = Math.max(1, current_page - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(last_page, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return (
      <div className="flex items-center justify-center gap-2 mt-8">
        {/* Previous Button */}
        <button
          onClick={() => handlePageChange(current_page - 1)}
          disabled={current_page === 1}
          className="p-2 rounded-lg border border-gray-200 dark:border-gray-700 
                     bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm
                     hover:bg-gray-50 dark:hover:bg-gray-700/50 
                     disabled:opacity-50 disabled:cursor-not-allowed
                     transition-all duration-200"
        >
          <ChevronLeftIcon className="h-5 w-5" />
        </button>

        {/* Page Numbers */}
        {startPage > 1 && (
          <>
            <button
              onClick={() => handlePageChange(1)}
              className="px-3 py-2 rounded-lg border border-gray-200 dark:border-gray-700 
                         bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm
                         hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-all duration-200"
            >
              1
            </button>
            {startPage > 2 && <span className="px-2 text-gray-500">...</span>}
          </>
        )}

        {pages.map(page => (
          <button
            key={page}
            onClick={() => handlePageChange(page)}
            className={`px-3 py-2 rounded-lg border transition-all duration-200 ${
              page === current_page
                ? 'bg-blue-500 text-white border-blue-500'
                : 'border-gray-200 dark:border-gray-700 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm hover:bg-gray-50 dark:hover:bg-gray-700/50'
            }`}
          >
            {page}
          </button>
        ))}

        {endPage < last_page && (
          <>
            {endPage < last_page - 1 && <span className="px-2 text-gray-500">...</span>}
            <button
              onClick={() => handlePageChange(last_page)}
              className="px-3 py-2 rounded-lg border border-gray-200 dark:border-gray-700 
                         bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm
                         hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-all duration-200"
            >
              {last_page}
            </button>
          </>
        )}

        {/* Next Button */}
        <button
          onClick={() => handlePageChange(current_page + 1)}
          disabled={current_page === last_page}
          className="p-2 rounded-lg border border-gray-200 dark:border-gray-700 
                     bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm
                     hover:bg-gray-50 dark:hover:bg-gray-700/50 
                     disabled:opacity-50 disabled:cursor-not-allowed
                     transition-all duration-200"
        >
          <ChevronRightIcon className="h-5 w-5" />
        </button>
      </div>
    );
  };

  if (loading) {
    return (
      <div className={`property-grid ${className}`}>
        <div className="flex items-center justify-center py-12">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  if (!properties || properties.length === 0) {
    return (
      <div className={`property-grid ${className}`}>
        <EmptyState
          title="No properties found"
          description="Try adjusting your search criteria or browse all properties."
          actionText="Browse All Properties"
          onAction={() => window.location.href = '/properties'}
        />
      </div>
    );
  }

  return (
    <div className={`property-grid ${className}`}>
      {/* Header with Controls */}
      {showFilters && (
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-6">
          {/* Results Count */}
          <div className="text-gray-600 dark:text-gray-400">
            {pagination ? (
              <span>
                Showing {pagination.from}-{pagination.to} of {pagination.total} properties
              </span>
            ) : (
              <span>{properties.length} properties found</span>
            )}
          </div>

          {/* Controls */}
          <div className="flex items-center gap-4">
            {/* Sort Dropdown */}
            <div className="flex items-center gap-2">
              <AdjustmentsHorizontalIcon className="h-5 w-5 text-gray-500" />
              <select
                value={`${sortBy}-${sortOrder}`}
                onChange={(e) => {
                  const [field, order] = e.target.value.split('-');
                  setSortBy(field);
                  setSortOrder(order);
                }}
                className="px-3 py-2 rounded-lg border border-gray-200 dark:border-gray-700
                           bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm
                           focus:ring-2 focus:ring-blue-500 focus:border-transparent
                           text-sm"
              >
                {sortOptions.map(option => (
                  <React.Fragment key={option.value}>
                    <option value={`${option.value}-desc`}>
                      {option.label} (High to Low)
                    </option>
                    <option value={`${option.value}-asc`}>
                      {option.label} (Low to High)
                    </option>
                  </React.Fragment>
                ))}
              </select>
            </div>

            {/* View Mode Toggle */}
            <div className="flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-md transition-all duration-200 ${
                  viewMode === 'grid'
                    ? 'bg-white dark:bg-gray-600 shadow-sm'
                    : 'hover:bg-gray-200 dark:hover:bg-gray-600'
                }`}
              >
                <Squares2X2Icon className="h-5 w-5" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-md transition-all duration-200 ${
                  viewMode === 'list'
                    ? 'bg-white dark:bg-gray-600 shadow-sm'
                    : 'hover:bg-gray-200 dark:hover:bg-gray-600'
                }`}
              >
                <ListBulletIcon className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Properties Grid/List */}
      <AnimatePresence mode="wait">
        <motion.div
          key={viewMode}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
        >
          {viewMode === 'grid' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {properties.map((property, index) => (
                <motion.div
                  key={property.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <PropertyCard
                    property={property}
                    onFavoriteToggle={onFavoriteToggle}
                    onShare={onShare}
                  />
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {properties.map((property, index) => (
                <motion.div
                  key={property.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <PropertyListItem
                    property={property}
                    onFavoriteToggle={onFavoriteToggle}
                    onShare={onShare}
                  />
                </motion.div>
              ))}
            </div>
          )}
        </motion.div>
      </AnimatePresence>

      {/* Pagination */}
      {renderPagination()}
    </div>
  );
};

export default PropertyGrid;
