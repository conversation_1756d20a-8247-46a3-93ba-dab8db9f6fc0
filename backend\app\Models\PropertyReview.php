<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PropertyReview extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'property_id',
        'user_id',
        'rating',
        'title',
        'review',
        'rating_breakdown',
        'is_verified',
        'is_anonymous',
        'status',
        'admin_notes',
        'helpful_votes',
        'not_helpful_votes',
        'stayed_from',
        'stayed_to',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'rating_breakdown' => 'array',
        'is_verified' => 'boolean',
        'is_anonymous' => 'boolean',
        'stayed_from' => 'datetime',
        'stayed_to' => 'datetime',
    ];

    /**
     * Get the property that was reviewed.
     */
    public function property()
    {
        return $this->belongsTo(Property::class);
    }

    /**
     * Get the user who wrote the review.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get approved reviews.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope to get pending reviews.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get verified reviews.
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope to order by rating.
     */
    public function scopeByRating($query, int $rating)
    {
        return $query->where('rating', $rating);
    }

    /**
     * Get the review author name (considering anonymity).
     */
    public function getAuthorNameAttribute(): string
    {
        if ($this->is_anonymous) {
            return 'Anonymous';
        }

        return $this->user ? $this->user->full_name : 'Unknown';
    }

    /**
     * Get the review helpfulness ratio.
     */
    public function getHelpfulnessRatioAttribute(): float
    {
        $totalVotes = $this->helpful_votes + $this->not_helpful_votes;

        if ($totalVotes === 0) {
            return 0;
        }

        return round(($this->helpful_votes / $totalVotes) * 100, 2);
    }
}
