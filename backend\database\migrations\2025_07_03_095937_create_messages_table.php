<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('messages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('sender_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('receiver_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('property_id')->nullable()->constrained()->onDelete('set null');
            $table->string('subject')->nullable();
            $table->text('message');
            $table->enum('type', ['inquiry', 'general', 'viewing_request', 'offer', 'negotiation', 'system'])->default('general');
            $table->enum('status', ['sent', 'delivered', 'read', 'replied', 'archived'])->default('sent');
            $table->datetime('read_at')->nullable();
            $table->datetime('replied_at')->nullable();
            $table->foreignId('parent_message_id')->nullable()->constrained('messages')->onDelete('cascade'); // For threading
            $table->json('attachments')->nullable(); // File attachments
            $table->json('metadata')->nullable(); // Additional data like viewing time, offer amount, etc.
            $table->boolean('is_important')->default(false);
            $table->boolean('is_archived')->default(false);

            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['sender_id', 'receiver_id']);
            $table->index(['receiver_id', 'status']);
            $table->index(['property_id', 'type']);
            $table->index(['parent_message_id']);
            $table->index(['created_at', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('messages');
    }
};
