<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Plan\CreatePlanRequest;
use App\Models\Plan;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class PlanController extends Controller
{
    /**
     * Display a listing of plans.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Plan::query();

            // Filter by type (user, agency)
            if ($request->has('type')) {
                $query->byType($request->type);
            }

            // Filter by active status
            if ($request->boolean('active_only', true)) {
                $query->active();
            }

            // Filter by popular
            if ($request->boolean('popular_only')) {
                $query->popular();
            }

            // Search by name
            if ($request->has('search')) {
                $query->where('name', 'like', '%' . $request->search . '%');
            }

            // Order plans
            $plans = $query->ordered()->get();

            // Add computed attributes
            $plans->each(function ($plan) {
                $plan->append(['formatted_price', 'monthly_price', 'yearly_price', 'effective_price', 'is_discounted', 'savings_amount', 'limits', 'capabilities']);
            });

            return response()->json([
                'plans' => $plans,
                'total' => $plans->count(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to fetch plans',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created plan.
     */
    public function store(CreatePlanRequest $request): JsonResponse
    {
        try {
            // Check permission
            if (!auth()->user()->can('create plans')) {
                return response()->json([
                    'message' => 'Unauthorized to create plans'
                ], 403);
            }

            $data = $request->validated();

            // Generate slug if not provided
            if (!isset($data['slug'])) {
                $data['slug'] = Str::slug($data['name']);
            }

            // Ensure slug is unique
            $originalSlug = $data['slug'];
            $counter = 1;
            while (Plan::where('slug', $data['slug'])->exists()) {
                $data['slug'] = $originalSlug . '-' . $counter;
                $counter++;
            }

            $plan = Plan::create($data);

            return response()->json([
                'message' => 'Plan created successfully',
                'plan' => $plan->load('subscriptions')
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create plan',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified plan.
     */
    public function show(Plan $plan): JsonResponse
    {
        try {
            $plan->load('subscriptions', 'activeSubscriptions');
            $plan->append(['formatted_price', 'monthly_price', 'yearly_price', 'effective_price', 'is_discounted', 'savings_amount', 'limits', 'capabilities']);

            return response()->json([
                'plan' => $plan
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Plan not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update the specified plan.
     */
    public function update(Request $request, Plan $plan): JsonResponse
    {
        try {
            // Check permission
            if (!auth()->user()->can('edit plans')) {
                return response()->json([
                    'message' => 'Unauthorized to edit plans'
                ], 403);
            }

            $validator = Validator::make($request->all(), [
                'name' => 'sometimes|string|max:255',
                'slug' => 'sometimes|string|max:255|unique:plans,slug,' . $plan->id,
                'description' => 'nullable|string',
                'type' => 'sometimes|in:user,agency',
                'price' => 'sometimes|numeric|min:0',
                'billing_cycle' => 'sometimes|in:monthly,quarterly,yearly',
                'billing_cycle_months' => 'sometimes|integer|min:1',
                'features' => 'sometimes|array',
                'property_listing_limit' => 'nullable|integer|min:0',
                'featured_listing_limit' => 'nullable|integer|min:0',
                'photo_limit_per_property' => 'nullable|integer|min:0',
                'video_limit_per_property' => 'nullable|integer|min:0',
                'virtual_tour_enabled' => 'sometimes|boolean',
                'analytics_enabled' => 'sometimes|boolean',
                'lead_management_enabled' => 'sometimes|boolean',
                'priority_support_enabled' => 'sometimes|boolean',
                'api_access_enabled' => 'sometimes|boolean',
                'saved_properties_limit' => 'nullable|integer|min:0',
                'search_alerts_limit' => 'nullable|integer|min:0',
                'advanced_search_enabled' => 'sometimes|boolean',
                'property_recommendations_enabled' => 'sometimes|boolean',
                'market_insights_enabled' => 'sometimes|boolean',
                'is_popular' => 'sometimes|boolean',
                'is_active' => 'sometimes|boolean',
                'sort_order' => 'sometimes|integer|min:0',
                'stripe_price_id' => 'nullable|string',
                'color_scheme' => 'nullable|string',
                'icon' => 'nullable|string',
                'has_trial' => 'sometimes|boolean',
                'trial_days' => 'nullable|integer|min:0',
                'setup_fee' => 'sometimes|numeric|min:0',
                'discount_percentage' => 'nullable|numeric|min:0|max:100',
                'discount_valid_until' => 'nullable|date|after:today',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $plan->update($validator->validated());

            return response()->json([
                'message' => 'Plan updated successfully',
                'plan' => $plan->fresh()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update plan',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified plan.
     */
    public function destroy(Plan $plan): JsonResponse
    {
        try {
            // Check permission
            if (!auth()->user()->can('delete plans')) {
                return response()->json([
                    'message' => 'Unauthorized to delete plans'
                ], 403);
            }

            // Check if plan has active subscriptions
            if ($plan->activeSubscriptions()->exists()) {
                return response()->json([
                    'message' => 'Cannot delete plan with active subscriptions'
                ], 400);
            }

            $plan->delete();

            return response()->json([
                'message' => 'Plan deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete plan',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get plans suitable for a specific user type.
     */
    public function getByUserType(Request $request, string $userType): JsonResponse
    {
        try {
            $validator = Validator::make(['user_type' => $userType], [
                'user_type' => 'required|in:user,agency'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'message' => 'Invalid user type'
                ], 400);
            }

            $plans = Plan::active()
                ->byType($userType)
                ->ordered()
                ->get();

            $plans->each(function ($plan) {
                $plan->append(['formatted_price', 'monthly_price', 'yearly_price', 'effective_price', 'is_discounted', 'savings_amount', 'limits', 'capabilities']);
            });

            return response()->json([
                'plans' => $plans,
                'user_type' => $userType,
                'total' => $plans->count(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to fetch plans',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get popular plans.
     */
    public function popular(): JsonResponse
    {
        try {
            $plans = Plan::active()
                ->popular()
                ->ordered()
                ->get();

            $plans->each(function ($plan) {
                $plan->append(['formatted_price', 'monthly_price', 'yearly_price', 'effective_price', 'is_discounted', 'savings_amount', 'limits', 'capabilities']);
            });

            return response()->json([
                'plans' => $plans,
                'total' => $plans->count(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to fetch popular plans',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Compare multiple plans.
     */
    public function compare(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'plan_ids' => 'required|array|min:2|max:5',
                'plan_ids.*' => 'exists:plans,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $plans = Plan::whereIn('id', $request->plan_ids)
                ->active()
                ->ordered()
                ->get();

            $plans->each(function ($plan) {
                $plan->append(['formatted_price', 'monthly_price', 'yearly_price', 'effective_price', 'is_discounted', 'savings_amount', 'limits', 'capabilities']);
            });

            return response()->json([
                'plans' => $plans,
                'comparison' => $this->generateComparison($plans),
                'total' => $plans->count(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to compare plans',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate comparison data for plans.
     */
    private function generateComparison($plans): array
    {
        $features = [];
        $limits = [];
        $capabilities = [];

        foreach ($plans as $plan) {
            // Collect all unique features
            foreach ($plan->features ?? [] as $feature) {
                if (!in_array($feature, $features)) {
                    $features[] = $feature;
                }
            }

            // Collect limit types
            foreach ($plan->limits as $key => $value) {
                if (!isset($limits[$key])) {
                    $limits[$key] = [];
                }
                $limits[$key][$plan->id] = $value;
            }

            // Collect capabilities
            foreach ($plan->capabilities as $key => $value) {
                if (!isset($capabilities[$key])) {
                    $capabilities[$key] = [];
                }
                $capabilities[$key][$plan->id] = $value;
            }
        }

        return [
            'features' => $features,
            'limits' => $limits,
            'capabilities' => $capabilities,
        ];
    }
}
