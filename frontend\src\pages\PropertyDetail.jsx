import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'react-hot-toast';
import {
  HeartIcon,
  ShareIcon,
  MapPinIcon,
  HomeIcon,
  CalendarIcon,
  EyeIcon,
  StarIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  XMarkIcon,
  PlayIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import LoadingSpinner from '../components/common/LoadingSpinner';
import PropertyCard from '../components/property/PropertyCard';
import propertyService from '../services/propertyService';
import { useAuth } from '../contexts/AuthContext';

const PropertyDetail = () => {
  const { slug } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  
  const [property, setProperty] = useState(null);
  const [similarProperties, setSimilarProperties] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isFavorited, setIsFavorited] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showImageModal, setShowImageModal] = useState(false);

  // Load property details
  const loadProperty = async () => {
    try {
      setLoading(true);
      const response = await propertyService.getPropertyBySlug(slug);
      const propertyData = response.data.property;
      const similarData = response.data.similar_properties || [];
      
      setProperty(propertyData);
      setSimilarProperties(similarData);
      setIsFavorited(propertyData.is_favorited || false);
    } catch (error) {
      console.error('Failed to load property:', error);
      toast.error('Property not found');
      navigate('/properties');
    } finally {
      setLoading(false);
    }
  };

  // Handle favorite toggle
  const handleFavoriteToggle = async () => {
    if (!user) {
      toast.error('Please login to save properties to favorites');
      navigate('/login');
      return;
    }

    try {
      const newFavoriteState = !isFavorited;
      setIsFavorited(newFavoriteState);
      
      if (newFavoriteState) {
        await propertyService.saveToFavorites(property.id);
        toast.success('Property added to favorites');
      } else {
        await propertyService.removeFromFavorites(property.id);
        toast.success('Property removed from favorites');
      }
    } catch (error) {
      // Revert on error
      setIsFavorited(isFavorited);
      console.error('Failed to toggle favorite:', error);
      toast.error('Failed to update favorites. Please try again.');
    }
  };

  // Handle share
  const handleShare = async () => {
    const shareData = {
      title: property.title,
      text: `Check out this property: ${property.title}`,
      url: window.location.href
    };

    try {
      if (navigator.share && navigator.canShare(shareData)) {
        await navigator.share(shareData);
      } else {
        await navigator.clipboard.writeText(shareData.url);
        toast.success('Property link copied to clipboard');
      }
    } catch (error) {
      console.error('Failed to share:', error);
      try {
        await navigator.clipboard.writeText(shareData.url);
        toast.success('Property link copied to clipboard');
      } catch (clipboardError) {
        toast.error('Failed to share property');
      }
    }
  };

  // Image navigation
  const nextImage = () => {
    if (property.images && property.images.length > 0) {
      setCurrentImageIndex((prev) => 
        prev === property.images.length - 1 ? 0 : prev + 1
      );
    }
  };

  const prevImage = () => {
    if (property.images && property.images.length > 0) {
      setCurrentImageIndex((prev) => 
        prev === 0 ? property.images.length - 1 : prev - 1
      );
    }
  };

  // Load property on mount
  useEffect(() => {
    if (slug) {
      loadProperty();
    }
  }, [slug]);

  // Keyboard navigation for images
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (showImageModal) {
        if (e.key === 'ArrowLeft') prevImage();
        if (e.key === 'ArrowRight') nextImage();
        if (e.key === 'Escape') setShowImageModal(false);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [showImageModal, property]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!property) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Property Not Found
          </h1>
          <button
            onClick={() => navigate('/properties')}
            className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            Browse Properties
          </button>
        </div>
      </div>
    );
  }

  const images = property.images || [];
  const currentImage = images[currentImageIndex];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 
                    dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={() => navigate(-1)}
              className="flex items-center gap-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
            >
              <ChevronLeftIcon className="h-5 w-5" />
              Back to Properties
            </button>

            <div className="flex items-center gap-3">
              <motion.button
                onClick={handleFavoriteToggle}
                className="p-3 glass-card hover:shadow-lg transition-all duration-200"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {isFavorited ? (
                  <HeartSolidIcon className="h-6 w-6 text-red-500" />
                ) : (
                  <HeartIcon className="h-6 w-6 text-gray-600 dark:text-gray-400" />
                )}
              </motion.button>

              <motion.button
                onClick={handleShare}
                className="p-3 glass-card hover:shadow-lg transition-all duration-200"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <ShareIcon className="h-6 w-6 text-gray-600 dark:text-gray-400" />
              </motion.button>
            </div>
          </div>

          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-2">
            {property.title}
          </h1>
          
          <div className="flex items-center text-gray-600 dark:text-gray-400 mb-4">
            <MapPinIcon className="h-5 w-5 mr-2" />
            <span>{property.full_address}</span>
          </div>

          <div className="flex items-center gap-6 text-sm text-gray-600 dark:text-gray-400">
            {property.views_count > 0 && (
              <div className="flex items-center gap-1">
                <EyeIcon className="h-4 w-4" />
                <span>{property.views_count} views</span>
              </div>
            )}
            {property.favorites_count > 0 && (
              <div className="flex items-center gap-1">
                <HeartIcon className="h-4 w-4" />
                <span>{property.favorites_count} favorites</span>
              </div>
            )}
            {property.average_rating > 0 && (
              <div className="flex items-center gap-1">
                <StarIcon className="h-4 w-4" />
                <span>{property.average_rating.toFixed(1)} rating</span>
              </div>
            )}
            <div className="flex items-center gap-1">
              <CalendarIcon className="h-4 w-4" />
              <span>Listed {new Date(property.created_at).toLocaleDateString()}</span>
            </div>
          </div>
        </motion.div>

        {/* Image Gallery */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="mb-8"
        >
          <div className="glass-card overflow-hidden">
            {images.length > 0 ? (
              <div className="relative">
                {/* Main Image */}
                <div 
                  className="relative aspect-[16/9] cursor-pointer"
                  onClick={() => setShowImageModal(true)}
                >
                  <img
                    src={currentImage?.large_url || currentImage?.url}
                    alt={property.title}
                    className="w-full h-full object-cover"
                  />
                  
                  {/* Play button for videos */}
                  {property.video_url && currentImageIndex === 0 && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-20 h-20 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center">
                        <PlayIcon className="h-10 w-10 text-white ml-1" />
                      </div>
                    </div>
                  )}

                  {/* Navigation arrows */}
                  {images.length > 1 && (
                    <>
                      <button
                        onClick={(e) => { e.stopPropagation(); prevImage(); }}
                        className="absolute left-4 top-1/2 transform -translate-y-1/2 p-2 bg-black/50 backdrop-blur-sm text-white rounded-full hover:bg-black/70 transition-colors"
                      >
                        <ChevronLeftIcon className="h-6 w-6" />
                      </button>
                      <button
                        onClick={(e) => { e.stopPropagation(); nextImage(); }}
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 p-2 bg-black/50 backdrop-blur-sm text-white rounded-full hover:bg-black/70 transition-colors"
                      >
                        <ChevronRightIcon className="h-6 w-6" />
                      </button>
                    </>
                  )}

                  {/* Image counter */}
                  <div className="absolute bottom-4 right-4 px-3 py-1 bg-black/50 backdrop-blur-sm text-white text-sm rounded-full">
                    {currentImageIndex + 1} / {images.length}
                  </div>
                </div>

                {/* Thumbnail strip */}
                {images.length > 1 && (
                  <div className="p-4 bg-gray-50 dark:bg-gray-800">
                    <div className="flex gap-2 overflow-x-auto">
                      {images.map((image, index) => (
                        <button
                          key={image.id}
                          onClick={() => setCurrentImageIndex(index)}
                          className={`flex-shrink-0 w-20 h-16 rounded-lg overflow-hidden border-2 transition-all ${
                            index === currentImageIndex
                              ? 'border-blue-500'
                              : 'border-transparent hover:border-gray-300'
                          }`}
                        >
                          <img
                            src={image.thumbnail_url || image.url}
                            alt={`${property.title} ${index + 1}`}
                            className="w-full h-full object-cover"
                          />
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="aspect-[16/9] bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                <div className="text-center text-gray-500 dark:text-gray-400">
                  <HomeIcon className="h-16 w-16 mx-auto mb-4" />
                  <p>No images available</p>
                </div>
              </div>
            )}
          </div>
        </motion.div>

        {/* Property Details Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Price and Basic Info */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="glass-card p-6"
            >
              <div className="flex items-center justify-between mb-6">
                <div>
                  <div className="text-3xl font-bold text-gray-900 dark:text-white">
                    {propertyService.formatPrice(property.price, property.currency)}
                  </div>
                  {property.listing_type === 'rent' && (
                    <div className="text-gray-600 dark:text-gray-400">/month</div>
                  )}
                  {property.price_per_sqft && (
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {propertyService.formatPrice(property.price_per_sqft)}/sqft
                    </div>
                  )}
                </div>

                <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                  property.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                  property.status === 'sold' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                  property.status === 'rented' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' :
                  'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                }`}>
                  {property.status_badge?.text || property.status}
                </div>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {property.bedrooms && (
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">
                      {property.bedrooms}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      Bedrooms
                    </div>
                  </div>
                )}
                {property.bathrooms && (
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">
                      {property.bathrooms}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      Bathrooms
                    </div>
                  </div>
                )}
                {property.total_area && (
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">
                      {propertyService.formatArea(property.total_area)}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      Total Area
                    </div>
                  </div>
                )}
                {property.year_built && (
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">
                      {property.year_built}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      Year Built
                    </div>
                  </div>
                )}
              </div>
            </motion.div>

            {/* Description */}
            {property.description && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="glass-card p-6"
              >
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  Description
                </h2>
                <div className="prose prose-gray dark:prose-invert max-w-none">
                  <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                    {property.description}
                  </p>
                </div>
              </motion.div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Agency Info */}
            {property.agency && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
                className="glass-card p-6"
              >
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Listed by
                </h3>
                <div className="flex items-center gap-4 mb-4">
                  {property.agency.logo && (
                    <img
                      src={property.agency.logo}
                      alt={property.agency.name}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                  )}
                  <div>
                    <div className="font-medium text-gray-900 dark:text-white">
                      {property.agency.name}
                    </div>
                    {property.agency.is_verified && (
                      <div className="flex items-center gap-1 text-sm text-blue-600 dark:text-blue-400">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        Verified Agency
                      </div>
                    )}
                  </div>
                </div>
                
                <button className="w-full px-4 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                  Contact Agent
                </button>
              </motion.div>
            )}
          </div>
        </div>

        {/* Similar Properties */}
        {similarProperties.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="mb-8"
          >
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
              Similar Properties
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {similarProperties.map((similarProperty) => (
                <PropertyCard
                  key={similarProperty.id}
                  property={similarProperty}
                  onFavoriteToggle={handleFavoriteToggle}
                  onShare={handleShare}
                  showAgency={false}
                  showStats={false}
                />
              ))}
            </div>
          </motion.div>
        )}
      </div>

      {/* Image Modal */}
      <AnimatePresence>
        {showImageModal && currentImage && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-4"
            onClick={() => setShowImageModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0.9 }}
              className="relative max-w-7xl max-h-full"
              onClick={(e) => e.stopPropagation()}
            >
              <img
                src={currentImage.large_url || currentImage.url}
                alt={property.title}
                className="max-w-full max-h-full object-contain"
              />
              
              {/* Close button */}
              <button
                onClick={() => setShowImageModal(false)}
                className="absolute top-4 right-4 p-2 bg-black/50 backdrop-blur-sm text-white rounded-full hover:bg-black/70 transition-colors"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>

              {/* Navigation */}
              {images.length > 1 && (
                <>
                  <button
                    onClick={prevImage}
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 p-2 bg-black/50 backdrop-blur-sm text-white rounded-full hover:bg-black/70 transition-colors"
                  >
                    <ChevronLeftIcon className="h-6 w-6" />
                  </button>
                  <button
                    onClick={nextImage}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 p-2 bg-black/50 backdrop-blur-sm text-white rounded-full hover:bg-black/70 transition-colors"
                  >
                    <ChevronRightIcon className="h-6 w-6" />
                  </button>
                </>
              )}

              {/* Image counter */}
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 px-4 py-2 bg-black/50 backdrop-blur-sm text-white text-sm rounded-full">
                {currentImageIndex + 1} / {images.length}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default PropertyDetail;
