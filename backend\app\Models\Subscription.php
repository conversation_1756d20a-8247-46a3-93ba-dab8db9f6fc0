<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class Subscription extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'plan_id',
        'stripe_subscription_id',
        'stripe_customer_id',
        'status',
        'starts_at',
        'ends_at',
        'trial_ends_at',
        'cancelled_at',
        'amount',
        'currency',
        'billing_cycle',
        'next_billing_date',
        'last_payment_date',
        'usage_limits',
        'current_usage',
        'auto_renew',
        'cancellation_reason',
        'cancellation_feedback',
        'coupon_code',
        'discount_amount',
        'discount_percentage',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
        'trial_ends_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'next_billing_date' => 'datetime',
        'last_payment_date' => 'datetime',
        'usage_limits' => 'array',
        'current_usage' => 'array',
        'auto_renew' => 'boolean',
        'amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
    ];

    /**
     * Get the user that owns the subscription.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the plan for this subscription.
     */
    public function plan()
    {
        return $this->belongsTo(Plan::class);
    }

    /**
     * Scope to get active subscriptions.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get expired subscriptions.
     */
    public function scopeExpired($query)
    {
        return $query->where('status', 'expired')
                    ->orWhere(function ($q) {
                        $q->where('ends_at', '<', now())
                          ->whereIn('status', ['active', 'trial']);
                    });
    }

    /**
     * Scope to get trial subscriptions.
     */
    public function scopeTrial($query)
    {
        return $query->where('status', 'trial');
    }

    /**
     * Scope to get cancelled subscriptions.
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    /**
     * Check if subscription is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active' && $this->ends_at->isFuture();
    }

    /**
     * Check if subscription is on trial.
     */
    public function onTrial(): bool
    {
        return $this->status === 'trial' &&
               $this->trial_ends_at &&
               $this->trial_ends_at->isFuture();
    }

    /**
     * Check if subscription is expired.
     */
    public function isExpired(): bool
    {
        return $this->status === 'expired' ||
               ($this->ends_at && $this->ends_at->isPast());
    }

    /**
     * Check if subscription is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * Check if subscription will auto-renew.
     */
    public function willAutoRenew(): bool
    {
        return $this->auto_renew && $this->isActive();
    }

    /**
     * Get days remaining in subscription.
     */
    public function getDaysRemainingAttribute(): int
    {
        if ($this->onTrial()) {
            return max(0, $this->trial_ends_at->diffInDays(now()));
        }

        if ($this->isActive()) {
            return max(0, $this->ends_at->diffInDays(now()));
        }

        return 0;
    }

    /**
     * Get trial days remaining.
     */
    public function getTrialDaysRemainingAttribute(): int
    {
        if ($this->onTrial()) {
            return max(0, $this->trial_ends_at->diffInDays(now()));
        }

        return 0;
    }

    /**
     * Check if subscription can be cancelled.
     */
    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['active', 'trial']) && !$this->isCancelled();
    }

    /**
     * Check if subscription can be renewed.
     */
    public function canBeRenewed(): bool
    {
        return in_array($this->status, ['expired', 'cancelled']);
    }

    /**
     * Get usage percentage for a specific limit.
     */
    public function getUsagePercentage(string $limitType): float
    {
        $currentUsage = $this->current_usage[$limitType] ?? 0;
        $limit = $this->usage_limits[$limitType] ?? 0;

        if ($limit === 0) {
            return 0;
        }

        return min(100, ($currentUsage / $limit) * 100);
    }

    /**
     * Check if usage limit is exceeded for a specific type.
     */
    public function isUsageLimitExceeded(string $limitType): bool
    {
        $currentUsage = $this->current_usage[$limitType] ?? 0;
        $limit = $this->usage_limits[$limitType] ?? 0;

        return $limit > 0 && $currentUsage >= $limit;
    }

    /**
     * Get remaining usage for a specific limit.
     */
    public function getRemainingUsage(string $limitType): int
    {
        $currentUsage = $this->current_usage[$limitType] ?? 0;
        $limit = $this->usage_limits[$limitType] ?? 0;

        return max(0, $limit - $currentUsage);
    }

    /**
     * Increment usage for a specific type.
     */
    public function incrementUsage(string $limitType, int $amount = 1): bool
    {
        $currentUsage = $this->current_usage;
        $currentUsage[$limitType] = ($currentUsage[$limitType] ?? 0) + $amount;

        return $this->update(['current_usage' => $currentUsage]);
    }

    /**
     * Decrement usage for a specific type.
     */
    public function decrementUsage(string $limitType, int $amount = 1): bool
    {
        $currentUsage = $this->current_usage;
        $currentUsage[$limitType] = max(0, ($currentUsage[$limitType] ?? 0) - $amount);

        return $this->update(['current_usage' => $currentUsage]);
    }

    /**
     * Reset usage for all types.
     */
    public function resetUsage(): bool
    {
        return $this->update(['current_usage' => []]);
    }

    /**
     * Cancel the subscription.
     */
    public function cancel(string $reason = null, string $feedback = null): bool
    {
        return $this->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
            'auto_renew' => false,
            'cancellation_reason' => $reason,
            'cancellation_feedback' => $feedback,
        ]);
    }

    /**
     * Renew the subscription.
     */
    public function renew(Carbon $newEndDate = null): bool
    {
        $endDate = $newEndDate ?: $this->ends_at->addMonths($this->plan->billing_cycle_months);

        return $this->update([
            'status' => 'active',
            'ends_at' => $endDate,
            'next_billing_date' => $endDate,
            'cancelled_at' => null,
            'cancellation_reason' => null,
            'cancellation_feedback' => null,
        ]);
    }

    /**
     * Extend the subscription by a number of days.
     */
    public function extend(int $days): bool
    {
        return $this->update([
            'ends_at' => $this->ends_at->addDays($days),
            'next_billing_date' => $this->ends_at->addDays($days),
        ]);
    }
}
