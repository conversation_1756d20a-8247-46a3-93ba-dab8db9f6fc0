@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
    line-height: 1.5;
    font-weight: 400;
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    margin: 0;
    min-width: 320px;
    min-height: 100vh;
  }
}

@layer components {

  /* Glassmorphism components */
  .glass {
    @apply backdrop-blur-md bg-white/10 border border-white/20;
  }

  .glass-dark {
    @apply backdrop-blur-md bg-black/10 border border-black/20;
  }

  .glass-card {
    @apply backdrop-blur-md bg-white/70 dark:bg-gray-800/70 border border-white/20 dark:border-gray-700/20 rounded-xl shadow-lg;
  }

  /* Neumorphism components */
  .neomorphism {
    @apply bg-gray-100 shadow-[8px_8px_16px_#d1d9e6, -8px_-8px_16px_#ffffff];
  }

  .neomorphism-dark {
    @apply bg-gray-800 shadow-[8px_8px_16px_#1a1a1a, -8px_-8px_16px_#2a2a2a];
  }

  .neomorphism-inset {
    @apply bg-gray-100 shadow-[inset_8px_8px_16px_#d1d9e6, inset_-8px_-8px_16px_#ffffff];
  }

  .neomorphism-inset-dark {
    @apply bg-gray-800 shadow-[inset_8px_8px_16px_#1a1a1a, inset_-8px_-8px_16px_#2a2a2a];
  }

  /* Button styles */
  .btn-primary {
    @apply px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }

  .btn-secondary {
    @apply px-6 py-3 bg-gray-200 text-gray-800 rounded-lg font-medium hover:bg-gray-300 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
  }

  .btn-glass {
    @apply px-6 py-3 glass text-white rounded-lg font-medium hover:bg-white/20 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white/50;
  }
}