<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('property_reviews', function (Blueprint $table) {
            $table->id();
            $table->foreignId('property_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->integer('rating'); // 1-5 stars
            $table->string('title')->nullable();
            $table->text('review');
            $table->json('rating_breakdown')->nullable(); // e.g., {"location": 5, "value": 4, "condition": 5}
            $table->boolean('is_verified')->default(false); // Verified purchase/rental
            $table->boolean('is_anonymous')->default(false);
            $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending');
            $table->text('admin_notes')->nullable();
            $table->integer('helpful_votes')->default(0);
            $table->integer('not_helpful_votes')->default(0);
            $table->datetime('stayed_from')->nullable(); // For rental reviews
            $table->datetime('stayed_to')->nullable();

            $table->timestamps();
            $table->softDeletes();

            // Unique constraint to prevent multiple reviews from same user for same property
            $table->unique(['property_id', 'user_id']);

            // Indexes
            $table->index(['property_id', 'status']);
            $table->index(['user_id', 'created_at']);
            $table->index(['rating', 'status']);
            $table->index('is_verified');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('property_reviews');
    }
};
