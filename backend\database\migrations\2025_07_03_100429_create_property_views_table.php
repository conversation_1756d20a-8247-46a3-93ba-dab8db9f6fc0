<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('property_views', function (Blueprint $table) {
            $table->id();
            $table->foreignId('property_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null'); // Null for anonymous views
            $table->string('ip_address');
            $table->string('user_agent')->nullable();
            $table->string('referrer')->nullable();
            $table->string('session_id')->nullable();
            $table->integer('duration_seconds')->nullable(); // How long they viewed the property
            $table->json('device_info')->nullable(); // Brows<PERSON>, OS, device type
            $table->string('country')->nullable();
            $table->string('city')->nullable();
            $table->enum('view_type', ['listing', 'gallery', 'virtual_tour', 'map'])->default('listing');

            $table->timestamps();

            // Indexes
            $table->index(['property_id', 'created_at']);
            $table->index(['user_id', 'created_at']);
            $table->index('ip_address');
            $table->index('session_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('property_views');
    }
};
