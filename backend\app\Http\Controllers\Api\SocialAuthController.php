<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Laravel\Socialite\Facades\Socialite;
use Spatie\Permission\Models\Role;

class SocialAuthController extends Controller
{
    /**
     * Redirect to social provider
     */
    public function redirectToProvider(string $provider): JsonResponse
    {
        try {
            $validProviders = ['google', 'facebook', 'apple'];

            if (!in_array($provider, $validProviders)) {
                return response()->json([
                    'message' => 'Invalid social provider'
                ], 400);
            }

            $redirectUrl = Socialite::driver($provider)->stateless()->redirect()->getTargetUrl();

            return response()->json([
                'redirect_url' => $redirectUrl
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to get redirect URL',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle social provider callback
     */
    public function handleProviderCallback(string $provider, Request $request): JsonResponse
    {
        try {
            $validProviders = ['google', 'facebook', 'apple'];

            if (!in_array($provider, $validProviders)) {
                return response()->json([
                    'message' => 'Invalid social provider'
                ], 400);
            }

            // Get user from social provider
            $socialUser = Socialite::driver($provider)->stateless()->user();

            if (!$socialUser) {
                return response()->json([
                    'message' => 'Failed to get user from social provider'
                ], 400);
            }

            // Check if user exists with this social ID
            $user = $this->findOrCreateUser($socialUser, $provider);

            if (!$user) {
                return response()->json([
                    'message' => 'Failed to create or find user'
                ], 500);
            }

            // Check if user is active
            if ($user->status !== 'active') {
                return response()->json([
                    'message' => 'Account is not active. Please contact support.'
                ], 403);
            }

            // Update login tracking
            $user->update([
                'last_login_at' => now(),
                'last_login_ip' => $request->ip(),
                'login_count' => $user->login_count + 1,
            ]);

            // Create token
            $token = $user->createToken('auth_token')->plainTextToken;

            return response()->json([
                'message' => 'Social login successful',
                'user' => $user->load('roles', 'permissions'),
                'token' => $token,
                'token_type' => 'Bearer',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Social login failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle social login with token (for mobile apps)
     */
    public function loginWithToken(string $provider, Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'access_token' => 'required|string',
                'role' => 'nullable|in:user,agency',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $validProviders = ['google', 'facebook', 'apple'];

            if (!in_array($provider, $validProviders)) {
                return response()->json([
                    'message' => 'Invalid social provider'
                ], 400);
            }

            // Get user from social provider using token
            $socialUser = Socialite::driver($provider)->userFromToken($request->access_token);

            if (!$socialUser) {
                return response()->json([
                    'message' => 'Invalid access token'
                ], 400);
            }

            // Find or create user
            $user = $this->findOrCreateUser($socialUser, $provider, $request->role);

            if (!$user) {
                return response()->json([
                    'message' => 'Failed to create or find user'
                ], 500);
            }

            // Check if user is active
            if ($user->status !== 'active') {
                return response()->json([
                    'message' => 'Account is not active. Please contact support.'
                ], 403);
            }

            // Update login tracking
            $user->update([
                'last_login_at' => now(),
                'last_login_ip' => $request->ip(),
                'login_count' => $user->login_count + 1,
            ]);

            // Create token
            $token = $user->createToken('auth_token')->plainTextToken;

            return response()->json([
                'message' => 'Social login successful',
                'user' => $user->load('roles', 'permissions'),
                'token' => $token,
                'token_type' => 'Bearer',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Social login failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Find or create user from social provider data
     */
    private function findOrCreateUser($socialUser, string $provider, string $role = 'user'): ?User
    {
        try {
            $socialIdField = $provider . '_id';

            // First, try to find user by social ID
            $user = User::where($socialIdField, $socialUser->getId())->first();

            if ($user) {
                // Update user info if needed
                $user->update([
                    'profile_picture' => $user->profile_picture ?: $socialUser->getAvatar(),
                ]);
                return $user;
            }

            // Try to find user by email
            $user = User::where('email', $socialUser->getEmail())->first();

            if ($user) {
                // Link social account to existing user
                $user->update([
                    $socialIdField => $socialUser->getId(),
                    'profile_picture' => $user->profile_picture ?: $socialUser->getAvatar(),
                ]);
                return $user;
            }

            // Create new user
            $names = $this->parseFullName($socialUser->getName());

            $user = User::create([
                'first_name' => $names['first_name'],
                'last_name' => $names['last_name'],
                'email' => $socialUser->getEmail(),
                'password' => Hash::make(Str::random(32)), // Random password
                'role' => $role,
                'profile_picture' => $socialUser->getAvatar(),
                $socialIdField => $socialUser->getId(),
                'email_verified_at' => now(), // Social accounts are considered verified
                'status' => 'active',
            ]);

            // Assign role using Spatie Permission
            $roleModel = Role::findByName($user->role);
            $user->assignRole($roleModel);

            return $user;

        } catch (\Exception $e) {
            \Log::error('Failed to find or create social user: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Parse full name into first and last name
     */
    private function parseFullName(string $fullName): array
    {
        $names = explode(' ', trim($fullName), 2);

        return [
            'first_name' => $names[0] ?? 'User',
            'last_name' => $names[1] ?? '',
        ];
    }
}
