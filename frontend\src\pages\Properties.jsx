import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import PropertySearch from '../components/property/PropertySearch';
import PropertyGrid from '../components/property/PropertyGrid';
import LoadingSpinner from '../components/common/LoadingSpinner';
import propertyService from '../services/propertyService';
import { useAuth } from '../contexts/AuthContext';

const Properties = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  
  const [properties, setProperties] = useState([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState(null);
  const [filters, setFilters] = useState(() => {
    // Initialize filters from URL params
    const defaultFilters = propertyService.getDefaultFilters();
    const urlFilters = {};
    
    for (const [key, value] of searchParams.entries()) {
      if (key === 'featured_only' || key === 'premium_only') {
        urlFilters[key] = value === 'true';
      } else {
        urlFilters[key] = value;
      }
    }
    
    return { ...defaultFilters, ...urlFilters };
  });

  // Load properties
  const loadProperties = async (searchFilters = filters) => {
    try {
      setLoading(true);
      const params = propertyService.buildSearchParams(searchFilters);
      const response = await propertyService.getProperties(params);
      
      setProperties(response.data.properties || []);
      setPagination(response.data.pagination || null);
      
      // Update URL params
      const newSearchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== '' && value !== null && value !== undefined) {
          newSearchParams.set(key, value.toString());
        }
      });
      setSearchParams(newSearchParams);
      
    } catch (error) {
      console.error('Failed to load properties:', error);
      toast.error('Failed to load properties. Please try again.');
      setProperties([]);
      setPagination(null);
    } finally {
      setLoading(false);
    }
  };

  // Handle search
  const handleSearch = async (searchParams) => {
    const newFilters = { ...filters, ...searchParams, page: 1 };
    setFilters(newFilters);
    await loadProperties(newFilters);
  };

  // Handle page change
  const handlePageChange = async (page) => {
    const newFilters = { ...filters, page };
    setFilters(newFilters);
    await loadProperties(newFilters);
  };

  // Handle favorite toggle
  const handleFavoriteToggle = async (propertyId, isFavorited) => {
    if (!user) {
      toast.error('Please login to save properties to favorites');
      navigate('/login');
      return;
    }

    try {
      if (isFavorited) {
        // Add to favorites
        await propertyService.saveToFavorites(propertyId);
        toast.success('Property added to favorites');
      } else {
        // Remove from favorites
        await propertyService.removeFromFavorites(propertyId);
        toast.success('Property removed from favorites');
      }

      // Update local state
      setProperties(prev => prev.map(property => 
        property.id === propertyId 
          ? { ...property, is_favorited: isFavorited }
          : property
      ));
    } catch (error) {
      console.error('Failed to toggle favorite:', error);
      toast.error('Failed to update favorites. Please try again.');
      throw error; // Re-throw to revert UI state
    }
  };

  // Handle share
  const handleShare = async (property) => {
    const shareData = {
      title: property.title,
      text: `Check out this property: ${property.title}`,
      url: `${window.location.origin}/properties/${property.slug}`
    };

    try {
      if (navigator.share && navigator.canShare(shareData)) {
        await navigator.share(shareData);
      } else {
        // Fallback to clipboard
        await navigator.clipboard.writeText(shareData.url);
        toast.success('Property link copied to clipboard');
      }
    } catch (error) {
      console.error('Failed to share:', error);
      // Fallback to clipboard
      try {
        await navigator.clipboard.writeText(shareData.url);
        toast.success('Property link copied to clipboard');
      } catch (clipboardError) {
        toast.error('Failed to share property');
      }
    }
  };

  // Load properties on mount
  useEffect(() => {
    loadProperties();
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 
                    dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-8"
        >
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            Find Your Perfect
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              {' '}Property
            </span>
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Discover amazing properties with our advanced search and filtering system. 
            Find your dream home or investment opportunity today.
          </p>
        </motion.div>

        {/* Search Component */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <PropertySearch
            onSearch={handleSearch}
            initialFilters={filters}
            className="mb-8"
          />
        </motion.div>

        {/* Results */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <LoadingSpinner size="lg" />
            </div>
          ) : (
            <PropertyGrid
              properties={properties}
              loading={loading}
              pagination={pagination}
              onPageChange={handlePageChange}
              onFavoriteToggle={handleFavoriteToggle}
              onShare={handleShare}
              showFilters={true}
            />
          )}
        </motion.div>

        {/* Quick Stats */}
        {!loading && properties.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="mt-12"
          >
            <div className="glass-card p-6">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    {pagination?.total || properties.length}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Total Properties
                  </div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                    {properties.filter(p => p.listing_type === 'sale').length}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    For Sale
                  </div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                    {properties.filter(p => p.listing_type === 'rent').length}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    For Rent
                  </div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                    {properties.filter(p => p.is_featured).length}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Featured
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default Properties;
