<?php

namespace Database\Seeders;

use App\Models\Plan;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // User Plans
        $this->createUserPlans();

        // Agency Plans
        $this->createAgencyPlans();

        $this->command->info('Sample plans created successfully!');
    }

    /**
     * Create user plans
     */
    private function createUserPlans(): void
    {
        // Free User Plan
        Plan::create([
            'name' => 'Free Explorer',
            'slug' => 'free-explorer',
            'description' => 'Perfect for casual property browsing and getting started.',
            'type' => 'user',
            'price' => 0.00,
            'billing_cycle' => 'monthly',
            'billing_cycle_months' => 1,
            'features' => [
                'Basic property search',
                'View property details',
                'Contact agents',
                'Mobile app access',
            ],
            'saved_properties_limit' => 5,
            'search_alerts_limit' => 1,
            'advanced_search_enabled' => false,
            'property_recommendations_enabled' => false,
            'market_insights_enabled' => false,
            'priority_support_enabled' => false,
            'api_access_enabled' => false,
            'is_popular' => false,
            'is_active' => true,
            'sort_order' => 1,
            'color_scheme' => 'gray',
            'icon' => 'user',
            'has_trial' => false,
            'setup_fee' => 0.00,
        ]);

        // Premium User Plan
        Plan::create([
            'name' => 'Premium Hunter',
            'slug' => 'premium-hunter',
            'description' => 'Enhanced features for serious property hunters.',
            'type' => 'user',
            'price' => 9.99,
            'billing_cycle' => 'monthly',
            'billing_cycle_months' => 1,
            'features' => [
                'Advanced property search',
                'Unlimited property saves',
                'Multiple search alerts',
                'Property recommendations',
                'Market insights',
                'Priority support',
                'Mobile app access',
                'Email notifications',
            ],
            'saved_properties_limit' => null, // unlimited
            'search_alerts_limit' => 10,
            'advanced_search_enabled' => true,
            'property_recommendations_enabled' => true,
            'market_insights_enabled' => true,
            'priority_support_enabled' => true,
            'api_access_enabled' => false,
            'is_popular' => true,
            'is_active' => true,
            'sort_order' => 2,
            'color_scheme' => 'blue',
            'icon' => 'star',
            'has_trial' => true,
            'trial_days' => 14,
            'setup_fee' => 0.00,
        ]);

        // Pro User Plan
        Plan::create([
            'name' => 'Pro Investor',
            'slug' => 'pro-investor',
            'description' => 'Professional tools for real estate investors and power users.',
            'type' => 'user',
            'price' => 29.99,
            'billing_cycle' => 'monthly',
            'billing_cycle_months' => 1,
            'features' => [
                'All Premium features',
                'Investment analysis tools',
                'Market trend reports',
                'API access',
                'Custom alerts',
                'Portfolio tracking',
                'Advanced analytics',
                'Priority support',
            ],
            'saved_properties_limit' => null, // unlimited
            'search_alerts_limit' => null, // unlimited
            'advanced_search_enabled' => true,
            'property_recommendations_enabled' => true,
            'market_insights_enabled' => true,
            'priority_support_enabled' => true,
            'api_access_enabled' => true,
            'is_popular' => false,
            'is_active' => true,
            'sort_order' => 3,
            'color_scheme' => 'purple',
            'icon' => 'briefcase',
            'has_trial' => true,
            'trial_days' => 7,
            'setup_fee' => 0.00,
        ]);
    }

    /**
     * Create agency plans
     */
    private function createAgencyPlans(): void
    {
        // Starter Agency Plan
        Plan::create([
            'name' => 'Starter Agency',
            'slug' => 'starter-agency',
            'description' => 'Perfect for new agencies and individual agents.',
            'type' => 'agency',
            'price' => 49.99,
            'billing_cycle' => 'monthly',
            'billing_cycle_months' => 1,
            'features' => [
                'Property listings',
                'Basic analytics',
                'Lead management',
                'Photo uploads',
                'Contact forms',
                'Mobile app access',
            ],
            'property_listing_limit' => 25,
            'featured_listing_limit' => 2,
            'photo_limit_per_property' => 20,
            'video_limit_per_property' => 1,
            'virtual_tour_enabled' => false,
            'analytics_enabled' => true,
            'lead_management_enabled' => true,
            'priority_support_enabled' => false,
            'api_access_enabled' => false,
            'is_popular' => false,
            'is_active' => true,
            'sort_order' => 1,
            'color_scheme' => 'green',
            'icon' => 'home',
            'has_trial' => true,
            'trial_days' => 30,
            'setup_fee' => 0.00,
        ]);

        // Professional Agency Plan
        Plan::create([
            'name' => 'Professional Agency',
            'slug' => 'professional-agency',
            'description' => 'Advanced features for growing real estate agencies.',
            'type' => 'agency',
            'price' => 149.99,
            'billing_cycle' => 'monthly',
            'billing_cycle_months' => 1,
            'features' => [
                'All Starter features',
                'Virtual tours',
                'Advanced analytics',
                'Priority support',
                'Custom branding',
                'Lead scoring',
                'Email marketing',
                'Team management',
            ],
            'property_listing_limit' => 100,
            'featured_listing_limit' => 10,
            'photo_limit_per_property' => 50,
            'video_limit_per_property' => 5,
            'virtual_tour_enabled' => true,
            'analytics_enabled' => true,
            'lead_management_enabled' => true,
            'priority_support_enabled' => true,
            'api_access_enabled' => false,
            'is_popular' => true,
            'is_active' => true,
            'sort_order' => 2,
            'color_scheme' => 'blue',
            'icon' => 'building',
            'has_trial' => true,
            'trial_days' => 14,
            'setup_fee' => 0.00,
        ]);

        // Enterprise Agency Plan
        Plan::create([
            'name' => 'Enterprise Agency',
            'slug' => 'enterprise-agency',
            'description' => 'Complete solution for large real estate organizations.',
            'type' => 'agency',
            'price' => 399.99,
            'billing_cycle' => 'monthly',
            'billing_cycle_months' => 1,
            'features' => [
                'All Professional features',
                'Unlimited listings',
                'API access',
                'White-label solution',
                'Custom integrations',
                'Dedicated support',
                'Advanced reporting',
                'Multi-office management',
            ],
            'property_listing_limit' => null, // unlimited
            'featured_listing_limit' => null, // unlimited
            'photo_limit_per_property' => null, // unlimited
            'video_limit_per_property' => null, // unlimited
            'virtual_tour_enabled' => true,
            'analytics_enabled' => true,
            'lead_management_enabled' => true,
            'priority_support_enabled' => true,
            'api_access_enabled' => true,
            'is_popular' => false,
            'is_active' => true,
            'sort_order' => 3,
            'color_scheme' => 'gold',
            'icon' => 'crown',
            'has_trial' => true,
            'trial_days' => 7,
            'setup_fee' => 99.99,
        ]);
    }
}
