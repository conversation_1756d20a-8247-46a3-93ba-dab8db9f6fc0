import { configureStore } from '@reduxjs/toolkit';
import authSlice from './slices/authSlice';
import propertySlice from './slices/propertySlice';
import userSlice from './slices/userSlice';
import agencySlice from './slices/agencySlice';
import adminSlice from './slices/adminSlice';
import uiSlice from './slices/uiSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    property: propertySlice,
    user: userSlice,
    agency: agencySlice,
    admin: adminSlice,
    ui: uiSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
