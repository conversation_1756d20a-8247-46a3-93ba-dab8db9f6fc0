import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import {
  UsersIcon,
  BuildingOfficeIcon,
  HomeIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  EyeIcon,
  PlusIcon,
  CogIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';

const AdminDashboard = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    totalUsers: 1247,
    totalAgencies: 89,
    totalProperties: 3456,
    totalRevenue: 125000,
    pendingApprovals: 23,
    activeSubscriptions: 156,
    monthlyGrowth: 12.5,
    systemHealth: 98.5
  });

  const quickStats = [
    {
      name: 'Total Users',
      value: stats.totalUsers.toLocaleString(),
      icon: UsersIcon,
      color: 'bg-blue-500',
      change: '+12%',
      changeType: 'positive'
    },
    {
      name: 'Active Agencies',
      value: stats.totalAgencies.toLocaleString(),
      icon: BuildingOfficeIcon,
      color: 'bg-green-500',
      change: '+8%',
      changeType: 'positive'
    },
    {
      name: 'Total Properties',
      value: stats.totalProperties.toLocaleString(),
      icon: HomeIcon,
      color: 'bg-purple-500',
      change: '+15%',
      changeType: 'positive'
    },
    {
      name: 'Monthly Revenue',
      value: `$${stats.totalRevenue.toLocaleString()}`,
      icon: CurrencyDollarIcon,
      color: 'bg-yellow-500',
      change: '+22%',
      changeType: 'positive'
    },
    {
      name: 'Pending Approvals',
      value: stats.pendingApprovals.toString(),
      icon: ClockIcon,
      color: 'bg-orange-500',
      change: '-5%',
      changeType: 'negative'
    },
    {
      name: 'System Health',
      value: `${stats.systemHealth}%`,
      icon: ShieldCheckIcon,
      color: 'bg-emerald-500',
      change: '+0.2%',
      changeType: 'positive'
    }
  ];

  const quickActions = [
    {
      name: 'User Management',
      description: 'Manage users and permissions',
      href: '/admin/users',
      icon: UsersIcon,
      color: 'bg-blue-500'
    },
    {
      name: 'Agency Management',
      description: 'Approve and manage agencies',
      href: '/admin/agencies',
      icon: BuildingOfficeIcon,
      color: 'bg-green-500'
    },
    {
      name: 'Property Management',
      description: 'Oversee all properties',
      href: '/admin/properties',
      icon: HomeIcon,
      color: 'bg-purple-500'
    },
    {
      name: 'Plan Management',
      description: 'Manage subscription plans',
      href: '/admin/plans',
      icon: CurrencyDollarIcon,
      color: 'bg-yellow-500'
    },
    {
      name: 'Analytics',
      description: 'View detailed analytics',
      href: '/admin/analytics',
      icon: ChartBarIcon,
      color: 'bg-indigo-500'
    },
    {
      name: 'System Settings',
      description: 'Configure system settings',
      href: '/admin/settings',
      icon: CogIcon,
      color: 'bg-gray-500'
    }
  ];

  const recentActivities = [
    {
      id: 1,
      type: 'user_registered',
      message: 'New user John Doe registered',
      time: '2 minutes ago',
      icon: UsersIcon,
      color: 'text-blue-500'
    },
    {
      id: 2,
      type: 'agency_approved',
      message: 'Premium Realty agency approved',
      time: '15 minutes ago',
      icon: CheckCircleIcon,
      color: 'text-green-500'
    },
    {
      id: 3,
      type: 'property_flagged',
      message: 'Property #1234 flagged for review',
      time: '1 hour ago',
      icon: ExclamationTriangleIcon,
      color: 'text-orange-500'
    },
    {
      id: 4,
      type: 'subscription_purchased',
      message: 'Agency Pro plan purchased',
      time: '2 hours ago',
      icon: CurrencyDollarIcon,
      color: 'text-yellow-500'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center justify-between"
          >
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Admin Dashboard
              </h1>
              <p className="mt-2 text-gray-600 dark:text-gray-400">
                Welcome back, {user?.first_name}! Here's your system overview.
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <PlusIcon className="h-5 w-5 inline mr-2" />
                Quick Action
              </motion.button>
            </div>
          </motion.div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8">
          {quickStats.map((stat, index) => (
            <motion.div
              key={stat.name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {stat.name}
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {stat.value}
                  </p>
                  <p className={`text-sm ${stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'}`}>
                    {stat.change} from last month
                  </p>
                </div>
                <div className={`${stat.color} p-3 rounded-lg`}>
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Quick Actions
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {quickActions.map((action, index) => (
              <motion.a
                key={action.name}
                href={action.href}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ scale: 1.02 }}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 hover:shadow-md transition-all duration-200"
              >
                <div className="flex items-center">
                  <div className={`${action.color} p-3 rounded-lg`}>
                    <action.icon className="h-6 w-6 text-white" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                      {action.name}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {action.description}
                    </p>
                  </div>
                </div>
              </motion.a>
            ))}
          </div>
        </div>

        {/* Recent Activities */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Recent Activities
          </h2>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
            <div className="p-6">
              <div className="space-y-4">
                {recentActivities.map((activity, index) => (
                  <motion.div
                    key={activity.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center space-x-4 p-4 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    <div className={`p-2 rounded-lg bg-gray-100 dark:bg-gray-700`}>
                      <activity.icon className={`h-5 w-5 ${activity.color}`} />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {activity.message}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {activity.time}
                      </p>
                    </div>
                    <button className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                      <EyeIcon className="h-4 w-4" />
                    </button>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
