<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plans', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->enum('type', ['user', 'agency']); // Plan type for users or agencies
            $table->decimal('price', 10, 2);
            $table->enum('billing_cycle', ['monthly', 'quarterly', 'yearly']);
            $table->integer('billing_cycle_months'); // 1 for monthly, 3 for quarterly, 12 for yearly

            // Plan features
            $table->json('features'); // Array of features included in the plan
            $table->integer('property_listing_limit')->nullable(); // For agencies
            $table->integer('featured_listing_limit')->nullable(); // For agencies
            $table->integer('photo_limit_per_property')->nullable(); // For agencies
            $table->integer('video_limit_per_property')->nullable(); // For agencies
            $table->boolean('virtual_tour_enabled')->default(false); // For agencies
            $table->boolean('analytics_enabled')->default(false); // For agencies
            $table->boolean('lead_management_enabled')->default(false); // For agencies
            $table->boolean('priority_support_enabled')->default(false);
            $table->boolean('api_access_enabled')->default(false);

            // User plan features
            $table->integer('saved_properties_limit')->nullable(); // For users
            $table->integer('search_alerts_limit')->nullable(); // For users
            $table->boolean('advanced_search_enabled')->default(false); // For users
            $table->boolean('property_recommendations_enabled')->default(false); // For users
            $table->boolean('market_insights_enabled')->default(false); // For users

            // Plan settings
            $table->boolean('is_popular')->default(false);
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->string('stripe_price_id')->nullable(); // For Stripe integration
            $table->string('color_scheme')->nullable(); // For UI display
            $table->string('icon')->nullable(); // Plan icon

            // Trial settings
            $table->boolean('has_trial')->default(false);
            $table->integer('trial_days')->nullable();

            // Discounts
            $table->decimal('setup_fee', 10, 2)->default(0);
            $table->decimal('discount_percentage', 5, 2)->nullable();
            $table->date('discount_valid_until')->nullable();

            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['type', 'is_active']);
            $table->index('is_popular');
            $table->index('sort_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plans');
    }
};
